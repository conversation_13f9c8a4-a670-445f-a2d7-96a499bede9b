import { useLoadingStore } from "@/stores/loading";
import { useUserStore } from "@/stores/user";
import { createRouter, createWebHistory } from "vue-router";
import AuthView from "../views/AuthView.vue";
import NewPassword from "../views/NewPassword.vue";
import MfaConfigure from "../views/MfaConfigure.vue";
import userManagement from "../views/UserManagement.vue";
import userCreateView from "../views/UserCreation.vue";
import { Auth } from "aws-amplify";

const brandComponents = {
	sideBar: () => import("@/components/SideBar/BrandSideBar.vue"),
	topBar: () => import("@/components/TopBar/BrandsTopBar.vue"),
};

const comparisonComponents = {
	sideBar: () => import("@/components/SideBar/ComparisonSideBar.vue"),
	topBar: () => import("@/components/TopBar/ComparisonTopBar.vue"),
};

const searchComponents = {
	sideBar: () => import("@/components/SideBar/SearchSideBar.vue"),
};

const userManagementComponents = {
	topBar: () => import("@/components/TopBar/BrandsTopBar.vue"),
};

const userCreateComponents = {
	topBar: () => import("@/components/TopBar/BrandsTopBar.vue"),
};

const router = createRouter({
	history: createWebHistory(import.meta.env.BASE_URL),
	routes: [
		// {
		// path: "/",
		// redirect: () => {
		// 	const userStore = useUserStore();
		// 	if (userStore.isAuthenticated) {
		// 		return "/accounts";
		// 	} else {
		// 		return "/login";
		// 	}
		// },
		// },
		{
			path: "/dashboard/login",
			name: "login",
			component: AuthView,
		},
		{
			path: "/dashboard/new-password",
			name: "newPassword",
			component: NewPassword,
		},
		{
			path: "/dashboard/mfa-configure",
			name: "MfaConfigure",
			component: MfaConfigure,
		},
		{
			path: "/dashboard",
			name: "home",
			// route level code-splitting
			// this generates a separate chunk (About.[hash].js) for this route
			// which is lazy-loaded when the route is visited.
			component: () => import("@/layouts/DashboardLayout.vue"),
			children: [
				{
					path: "/dashboard/accounts",
					name: "accounts",
					meta: {
						parent: null,
						breadcrumb: "accounts",
					},
					components: {
						default: () => import("@/views/AccountsView.vue"),
						...brandComponents,
						...searchComponents,
					},
				},
				{
					path: "/dashboard/admin/users",
					name: "userManagement",
					meta: {
						parent: "accounts",
						breadcrumb: "userManagement",
						requiresAdmin: true,
					},
					components: {
						default: userManagement,
						...userManagementComponents,
					},
				},
				{
					path: "/dashboard/admin/users/create",
					name: "createUser",
					meta: {
						parent: "userManagement",
						breadcrumb: "createUser",
						requiresAdmin: true,
					},
					components: {
						default: userCreateView,
						...userCreateComponents,
					},
				},
				{
					path: "/dashboard/accounts/:account_id",
					name: "brands",
					meta: {
						parent: "accounts",
						breadcrumb: "brands",
					},
					components: {
						default: () => import("@/views/BrandsView.vue"),
						...brandComponents,
						...searchComponents,
					},
				},
				{
					path: "/dashboard/accounts/:account_id/comparison",
					name: "comparison",
					components: {
						default: () => import("@/views/ComparisonView.vue"),
						...comparisonComponents,
					},
					meta: {
						parent: "brands",
						breadcrumb: "Comparison",
					},
				},
				{
					path: "/dashboard/accounts/:account_id/brands/:brand_id",
					name: "brand",
					meta: {
						parent: "brands",
						breadcrumb: "brand",
					},
					redirect: (to) => {
						const { params } = to;
						return { name: "overview", params };
					},
				},
				{
					path: "/dashboard/accounts/:account_id/brands/:brand_id/overview",
					name: "overview",
					meta: {
						parent: "brand",
						breadcrumb: "Overview",
					},
					components: {
						default: () => import("@/views/OverviewView.vue"),
						...brandComponents,
					},
				},
				{
					path: "/dashboard/accounts/:account_id/brands/:brand_id/reservations",
					name: "reservations",
					components: {
						default: () => import("@/views/ReservationsView.vue"),
						...brandComponents,
					},
					meta: {
						parent: "brand",
						breadcrumb: "Reservations",
					},
				},
				{
					path: "/dashboard/accounts/:account_id/brands/:brand_id/scans",
					name: "scans",
					components: {
						default: () => import("@/views/ScansView.vue"),
						...brandComponents,
					},
					meta: {
						parent: "brand",
						breadcrumb: "Scans",
					},
				},
				{
					path: "/dashboard/accounts/:account_id/brands/:brand_id/precheckins",
					name: "precheckins",
					components: {
						default: () => import("@/views/PrecheckinsView.vue"),
						...brandComponents,
					},
					meta: {
						parent: "brand",
						breadcrumb: "Precheckins",
					},
				},
			],
		},
	],
	scrollBehavior() {
		// always scroll to top
		return { top: 0 };
	},
});

const LOGIN_PATH = "/dashboard/login";
const LOGIN_RECEPTION_URL = `${import.meta.env.VITE_APP_RECEPTION_URL}`;
const publicPages = [LOGIN_PATH];

router.beforeEach(async (to) => {
	const loadingStore = useLoadingStore();
	loadingStore.setLoading(true);
	// redirect to login page if not logged in and trying to access a restricted page

	const authRequired = !publicPages.includes(to.path);
	const userStore = useUserStore();

	if (userStore.isAuthenticated) {
		try {
			const session = await Auth.currentSession();
			const idToken = session.getIdToken();

			const expireTimeMillis = idToken.getExpiration() * 1000;
			const now = new Date().getTime();
			const tokenExpired = expireTimeMillis < now;

			if (tokenExpired) {
				throw new Error("Token expired");
			}
		} catch (error) {
			userStore.logout();
			return { name: "login" };
		}
	}
	if (
		(to.name === "newPassword" || to.name === "MfaConfigure") &&
		!userStore.isAuthenticated
	) {
		return;
	}

	if (authRequired && !userStore.isAuthenticated) {
		userStore.redirectUrl = `${to.fullPath}`;
		return LOGIN_PATH;
		// TODO : here we should check that if there exists the brand_id path param in the "to" route and check with the userStore.isAllowed method
	} else if (
		userStore.isAuthenticated &&
		to.path === "/"
		// brand_id &&
		// !userStore.isAllowed(brand_id)
	) {
		return { name: "accounts" };
	} else if (
		to.meta.requiresAdmin &&
		!userStore.isAdmin &&
		userStore.isAuthenticated
	) {
		return { name: "accounts" };
	} else if (userStore.isReception) {
		window.location.href = LOGIN_RECEPTION_URL;
	} else {
		userStore.redirectUrl = `${to.fullPath}`;
	}
});

router.afterEach(() => {
	const loadingStore = useLoadingStore();
	loadingStore.setLoading(false);
});

export default router;
