const items = [
	{
		id: 1,
		row: [
			"Main Switch",
			"Switch",
			"UAP-AC-Mesh-Pro",
			"Garden pool",
			"10.100.0.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"1 day ago",
		],
	},
	{
		id: 2,
		row: [
			"Ap 5",
			"Access point",
			"UAP-AC-Pro",
			"Garden pool",
			"10.100.0.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"1 day ago",
		],
	},
	{
		id: 3,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "DEAD", color: "danger", type: "tag" },
			{ content: "YES", color: "danger", type: "tag" },
			"5 minutes ago",
		],
	},
	{
		id: 4,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"5 minutes ago",
		],
	},
	{
		id: 5,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"5 minutes ago",
		],
	},
	{
		id: 6,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"5 minutes ago",
		],
	},
	{
		id: 7,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"5 minutes ago",
		],
	},
	{
		id: 8,
		row: [
			{ content: "main Router", type: "link", emits: "goToDevice" },
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "BAD", color: "warning", type: "tag" },
			{ content: "YES", color: "danger", type: "tag" },
			"5 minutes ago",
		],
	},
	{
		id: 9,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"5 minutes ago",
		],
	},
	{
		id: 10,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "PENDING", color: "warning", type: "tag" },
			"",
			"",
			"5 minutes ago",
		],
	},
];

export default items;
