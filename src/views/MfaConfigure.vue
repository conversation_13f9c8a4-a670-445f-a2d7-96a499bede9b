<template>
  <div
    class="flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8"
  >
    <div class="sm:mx-auto sm:w-full sm:max-w-sm">
      <img
        class="mx-auto w-auto"
        :src="HOTELINKING_AUTOCHECKIN_LOGO"
        alt="Autocheckin"
      />
      <h2
        data-test="mfaConfigureTitle"
        class="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"
      >
        {{ t("mfaConfigure.header") }}
      </h2>
      <div class="flex flex-col justify-center items-center">
        <p
          data-test="mfaConfigureDescription"
          class="mb-6 text-sm text-gray-500 text-center"
        >
          {{ t("mfaConfigure.description") }}
        </p>
        <img
          data-test="qrImage"
          width="150"
          :src="qrCodeUrl?.value"
          alt="QR Code"
        />
        <p class="my-6 text-sm text-gray-500 text-center">
          <span class="font-bold"> {{ t("common.code") }}: </span>
          <span data-test="qrCode">{{ qrCode }}</span>
        </p>
      </div>
    </div>
    <div class="sm:mx-auto sm:w-full sm:max-w-sm">
      <uiInput
        data-test="mfaCodeInput"
        class="mt-6"
        :label="t('mfaConfigure.codeLabel')"
        :loading="false"
        name="info"
        placeholder="..."
        @input-changed="validationCode = $event.value"
      />
      <div class="flex flex-col sm:flex-row justify-between items-center">
        <uiButton
          data-test="mfaConfigureButton"
          class="mt-4"
          :loading="false"
          @click="validateCode"
        >
          {{ t("mfaConfigure.authorize") }}
        </uiButton>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { HOTELINKING_AUTOCHECKIN_LOGO } from "@/constants";
import { ref, onMounted } from "vue";
import { useUserStore } from "@/stores/user";
import { useQRCode } from "@vueuse/integrations/useQRCode";
import { useRouter } from "vue-router";
import { useNotificationStore } from "@/stores/notification";
import { t } from "@/locales";
const notificationStore = useNotificationStore();
const router = useRouter();
const { setupMfa, verifyMfa, logout } = useUserStore();
const qrCode = ref("");
const qrCodeUrl = ref();
const validationCode = ref("");

onMounted(async () => {
  try {
    const { code, otpAuth } = await setupMfa();
    qrCode.value = code;
    qrCodeUrl.value = useQRCode(otpAuth, {
      color: {
        light: "#0000", // Transparent background
      },
    });
  } catch (e) {
    return router.push({ name: "login" });
  }
});

const validateCode = async () => {
  const isValidateCode = await verifyMfa(validationCode.value);
  if (isValidateCode) {
    notificationStore.success(
      t("mfaConfigure.notification.success.title"),
      t("mfaConfigure.notification.success.message")
    );
    await logout();

    router.push({ name: "login" });
    return;
  }
  notificationStore.error(
    t("mfaConfigure.notification.error.title"),
    t("mfaConfigure.notification.error.message")
  );
};
</script>
