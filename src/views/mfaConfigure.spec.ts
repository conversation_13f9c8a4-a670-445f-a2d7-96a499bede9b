import mfaConfigure from "./MfaConfigure.vue";
import { describe, it, vi, expect } from "vitest";
import { mount } from "@vue/test-utils";
import flushPromises from "flush-promises";
import { beforeEach } from "vitest";
import { createPinia, setActivePinia } from "pinia";
import { useUserStore } from "@/stores/user";
import { useNotificationStore } from "@/stores/notification";
import { t } from "@/locales";
import { useRouter } from "vue-router";

vi.mock("vue-router", async () => {
	const actual = await vi.importActual("vue-router");
	return {
		...actual,
		useRouter: vi.fn(() => ({
			push: () => {},
		})),
	};
});

describe("MfaConfigure Tests", () => {
	beforeEach(() => {
		vi.mock("@vueuse/router", () => ({
			useRouteQuery: vi.fn(() => ref()),
		}));
		setActivePinia(createPinia());
	});

	it("renders all components when the view is mounted and code is generated correctly", async () => {
		const userStore = useUserStore();
		userStore.setupMfa = vi.fn().mockResolvedValue({
			code: "123456",
			otpAuth:
				"otpauth://totp/<EMAIL>?secret=123456&issuer=Hotelinking Autocheckin",
		});

		const wrapper = await mount(mfaConfigure);
		await flushPromises();

		const mfaConfigureTitle = wrapper.find('[data-test="mfaConfigureTitle"]');
		const mfaConfigureDescription = wrapper.find(
			'[data-test="mfaConfigureDescription"]',
		);
		const qrImage = wrapper.find('[data-test="qrImage"]');
		const qrCode = wrapper.find('[data-test="qrCode"]');
		const mfaCodeInput = wrapper.find('[data-test="mfaCodeInput"]');
		const mfaConfigureButton = wrapper.find('[data-test="mfaConfigureButton"]');

		expect(wrapper.exists()).toBe(true);
		expect(mfaConfigureTitle.isVisible()).toBe(true);
		expect(mfaConfigureDescription.isVisible()).toBe(true);
		expect(qrImage.isVisible()).toBe(true);
		expect(mfaCodeInput.isVisible()).toBe(true);
		expect(mfaConfigureButton.isVisible()).toBe(true);
		expect(qrCode.text()).toContain("123456");
	});

	it("should redirect to login page if setupMfa fails", async () => {
		const userStore = useUserStore();
		const push = vi.fn();
		useRouter.mockImplementationOnce(() => ({
			push,
		}));

		userStore.setupMfa = vi
			.fn()
			.mockRejectedValue(new Error("Setup MFA failed"));

		await mount(mfaConfigure);
		await flushPromises();

		expect(push).toHaveBeenCalledTimes(1);
		expect(push).toHaveBeenCalledWith({ name: "login" });
	});

	it("should trigger success notification and log out after valid MFA code", async () => {
		const push = vi.fn();
		useRouter.mockImplementationOnce(() => ({
			push,
		}));
		const userStore = useUserStore();
		const notificationStore = useNotificationStore();

		userStore.verifyMfa = vi.fn().mockResolvedValue(true);
		userStore.logout = vi.fn().mockResolvedValue(undefined);
		userStore.setupMfa = vi.fn().mockResolvedValue({
			code: "123456",
			otpAuth:
				"otpauth://totp/<EMAIL>?secret=123456&issuer=Hotelinking Autocheckin",
		});
		const notificationSuccess = vi.spyOn(notificationStore, "success");

		const wrapper = await mount(mfaConfigure);
		wrapper.vm.validationCode = "123456";

		await flushPromises();
		const mfaConfigureButton = wrapper.find('[data-test="mfaConfigureButton"]');
		await mfaConfigureButton.trigger("click");

		await flushPromises();

		expect(notificationSuccess).toHaveBeenCalledWith(
			"mfaConfigure.notification.success.title",
			"mfaConfigure.notification.success.message",
		);
		expect(userStore.logout).toHaveBeenCalled();
		expect(push).toHaveBeenCalledTimes(1);
		expect(push).toHaveBeenCalledWith({ name: "login" });
	});

	it("should trigger error notification after invalid MFA code", async () => {
		const push = vi.fn();
		useRouter.mockImplementationOnce(() => ({
			push,
		}));
		const userStore = useUserStore();
		const notificationStore = useNotificationStore();

		userStore.verifyMfa = vi.fn().mockResolvedValue(false);
		userStore.setupMfa = vi.fn().mockResolvedValue({
			code: "123456",
			otpAuth:
				"otpauth://totp/<EMAIL>?secret=123456&issuer=Hotelinking Autocheckin",
		});
		const notificationError = vi.spyOn(notificationStore, "error");

		const wrapper = await mount(mfaConfigure);
		wrapper.vm.validationCode = "123456";

		await flushPromises();
		const mfaConfigureButton = wrapper.find('[data-test="mfaConfigureButton"]');
		await mfaConfigureButton.trigger("click");

		await flushPromises();

		expect(notificationError).toHaveBeenCalledWith(
			"mfaConfigure.notification.error.title",
			"mfaConfigure.notification.error.message",
		);
		expect(push).toHaveBeenCalledTimes(0);
	});
});
