import { describe, it, expect } from "vitest";
import getErrorMessage from "@/modules/Error/getErrorMessage";

describe("getErrorMessage function", () => {
	it("should return the correct error message based on the error code", () => {
		const errorCode1 = "INT_1_1";
		const errorMessage1 = getErrorMessage(errorCode1);
		const errorCode2 = "INT_3_2";
		const errorMessage2 = getErrorMessage(errorCode2);
		const errorCode3 = "INT_1_7";
		const errorMessage3 = getErrorMessage(errorCode3);

		expect(errorMessage1).toBe("Integration exception");
		expect(errorMessage2).toBe("Reservation Is In Precheckin State");
		expect(errorMessage3).toBe("Forbidden Request Exception");
	});

	it("should return the error code if no matching error message is found", () => {
		const errorCode = "Unknown error code";
		const errorMessage = getErrorMessage(errorCode);
		expect(errorMessage).toBe(errorCode);
	});
});
