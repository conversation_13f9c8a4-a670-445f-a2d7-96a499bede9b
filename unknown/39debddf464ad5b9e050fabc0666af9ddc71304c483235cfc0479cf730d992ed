import type { uiChartGetSerieParams } from "@/uiTypes";
import { t } from "@/locales";
import { map, get } from "lodash-es";
import { format, add, parse, subDays } from "date-fns";
import getErrorMessage from "@/modules/Error/getErrorMessage";
import router from "../router";
import { i18n } from "@/locales";

import { calculateDaysBefore, getTimeDifference, getNextDate, getCurrentDateInUTC} from "./dates";
import type {
	Brand,
	ComparisonBrandRatio,
	SerieData,
	StatsData,
	DateRangeType,
} from "@/types";

import type { uiChartPointSelection } from "@/uiTypes";

interface OptionData {
	xaxis?: Xaxis;
	tooltip?: Tooltip;
}

interface Xaxis {
	categories: Array<String>;
}

interface Tooltip {
	y: Object;
}

interface ChartOptions {
	markers?: {
		size: number;
		hover: {
			size: number;
		};
	};
	xaxis?: {
		type: string;
		labels?: {
			format: string;
			datetimeUTC: boolean;
		};
	};
	tooltip?: {
		x: {
			format: string;
		};
	};
	title?: {
		text: string;
	};
}
interface ChartSeries {
	name: string;
	type: string;
	data: { x: string; y: number }[];
}

export const getSerie = ({
	successData,
	errorData,
}: uiChartGetSerieParams): Array<SerieData> => {
	return [
		{
			name: t("common.error"),
			data: errorData || [],
		},
		{
			name: t("common.success"),
			data: successData || [],
		},
	];
};

export const getSerieParams = (
	data: Array<ComparisonBrandRatio>,
): uiChartGetSerieParams => {
	const sortedData = sortData(data);
	const successData = map(sortedData, "success");
	const errorData = map(sortedData, "error");
	return {
		successData,
		errorData,
	};
};

export const getOptions = (names: Array<string>): OptionData => {
	interface Series {
		series: Array<Array<number>>;
		seriesIndex: number;
		dataPointIndex: number;
		w: {
			config: Object;
			globals: Object;
		};
	}
	const tooltip = {
		y: {
			formatter: function (val: number, series: Series) {
				try {
					const actualIndex = series.dataPointIndex;
					const totalErrors = series.series[0][actualIndex];
					const totalSuccess = series.series[1][actualIndex];
					const percentage = (val * 100) / (totalErrors + totalSuccess);
					return `${val} (${Math.round(percentage)}%)`;
				} catch (e) {
					return val;
				}
			},
		},
	};
	return {
		tooltip,
		xaxis: {
			categories: names,
		},
	};
};

export const getBrandNames = (
	brands: Array<number>,
	brandsSource: Array<Brand>,
	data: Array<ComparisonBrandRatio>,
): Array<string> => {
	const sortedData = sortData(data);
	const sortedBrands = sortBrandsByData(brands, sortedData);
	const names = sortedBrands.reduce((accum: Array<string>, item) => {
		const foundBrand: Brand | undefined = brandsSource.find(
			(brand: Brand) => item === brand.brandInfo.id,
		);
		if (foundBrand) {
			accum.push(foundBrand.brandInfo.name);
		}
		return accum;
	}, []);

	return names;
};

export const sortData = (
	data: Array<ComparisonBrandRatio>,
): Array<ComparisonBrandRatio> => {
	return data
		.map((item) => ({
			...item,
			success: item.success || 0,
			error: typeof item.error === "number" ? item.error : 0,
		}))
		.sort((a, b) => b.error + b.success - (a.error + a.success));
};

const sortBrandsByData = (
	brands: Array<number>,
	sortedData: Array<ComparisonBrandRatio>,
) => {
	const sortedBrands = brands.sort((brandIdA, brandIdB) => {
		const indexA = sortedData.findIndex((item) => item.brand_id === brandIdA);
		const indexB = sortedData.findIndex((item) => item.brand_id === brandIdB);
		if (indexA === -1) {
			return 1;
		}
		if (indexB === -1) {
			return -1;
		}
		return indexA - indexB;
	});
	return sortedBrands;
};

//TYPE AREA
const makeSeries = (
	data: StatsData[] | null,
	range: DateRangeType,
	formatSeriesY: [string, string] = ["success", "error.count"],
): ChartSeries[] | undefined => {
	const { interval } = calculateDaysBefore(range);
	try {
		if (!data) {
			return [];
		}

		const firstArray = formatSeries(data, "time", formatSeriesY[0]);
		const secondArray = formatSeries(data, "time", formatSeriesY[1]);
		const firstArrayFilled = fillIntervalGapsWithZeroValues(
			firstArray,
			interval,
		);
		const secondArrayFilled = fillIntervalGapsWithZeroValues(
			secondArray,
			interval,
		);

		return [
			{
				name: t(`common.${formatSeriesY[0]}`),
				type: "area",
				data: firstArrayFilled,
			},
			{
				name:
					formatSeriesY[1] === "error.count"
						? t("common.error")
						: t(`overview.uiChartText.${formatSeriesY[1]}`),
				type: "area",
				data: secondArrayFilled,
			},
		];
	} catch (error) {
		console.error("failed to make series for uiChart", error);
	}
};

//to prepare the data array in a format suitable for charts
const formatSeries = (data, x, y) => {
	return data.map((i) => ({ x: i[x], y: get(i, y, null) }));
};

const dateFormat = "yyyy-MM-dd HH:mm:ss.SSSSSSSSS";

//When generating errorsArray or successArray to pass to the uiChart, if the data contains two consecutive elements with dates differing by a duration greater than the selected time interval, we need to create an intermediate element with a date in the first missing interval and set its 'y' property to 0. E.g.
const fillIntervalGapsWithZeroValues = (data, interval) => {
	return data.reduce((accumulator, current, index, array) => {
		const { x: time, y: value } = current;
		//when index is 0, we just push the element because there is no previous element to compare date with
		if (index === 0) {
			accumulator.push({ x: time, y: value });
		} else {
			const currentDate = new Date(time);
			const previousDate = new Date(array[index - 1].x);

			const timeDifference = getTimeDifference(
				currentDate,
				previousDate,
				interval,
			);

			if (timeDifference > 1) {
				const dateAfterPrevious = format(
					getNextDate(previousDate, interval),
					dateFormat,
				);

				accumulator.push({ x: dateAfterPrevious, y: 0 });
			}

			accumulator.push({ x: time, y: value });
		}

		return accumulator;
	}, []);
};

const makeOptions = (chartType: string | null = null): ChartOptions => {
	if (chartType === "barType") {
		return {
			xaxis: {
				type: "date",
			},
		};
	} else {
		return {
			xaxis: {
				type: "datetime",
				labels: {
					format: "dd/MM HH:mm",
					datetimeUTC: false,
				},
			},
			tooltip: {
				x: {
					format: "dd/MM  HH:mm",
				},
			},
		};
	}
};

interface redirecToTableParams {
	eventData: uiChartPointSelection;
	tableName: string;
	dateRange: DateRangeType;
	calledFrom?: "checkinSourceChart" | "errorCharts";
}

const redirectToTable = ({
	eventData,
	tableName,
	dateRange,
	calledFrom,
}: redirecToTableParams): void => {
	const config = eventData.config;
	let formattedDateFrom;
	let formattedDateTo;

	if (calledFrom === "errorCharts") {
		//to set the period of time back that we should search from now
		const currentDate = new Date();
		const dateRangeMap = {
			"24h": 24 * 60 * 60 * 1000,
			"1m": 30 * 24 * 60 * 60 * 1000,
			"3m": 3 * 30 * 24 * 60 * 60 * 1000,
		};
		const dateDifference = dateRangeMap[dateRange];
		const fromDate = new Date(currentDate.getTime() - dateDifference);

		formattedDateTo = format(currentDate, "yyyy-MM-dd HH:mm:ss");
		formattedDateFrom = format(fromDate, "yyyy-MM-dd HH:mm:ss");
	} else {
		const selectedDate =
			config.w.config.series[0].data[config.dataPointIndex].x;

		// if interval is 24h, add an hour
		if (dateRange === "24h") {
			formattedDateFrom = selectedDate.slice(0, -10);
			const dateFrom = parse(
				formattedDateFrom,
				"yyyy-MM-dd HH:mm:ss",
				new Date(),
			);
			const dateTo = add(dateFrom, { hours: 1 });
			formattedDateTo = format(dateTo, "yyyy-MM-dd HH:mm:ss");
		} else {
			formattedDateFrom = selectedDate.slice(0, -10);
			formattedDateTo = `${selectedDate.slice(0, -18)}23:59:59`;
		}
	}

	const commonQueryParams = {
		date_from: formattedDateFrom,
		date_to: formattedDateTo,
	};
	
	router.push({
		name: tableName,
		query: calledFrom
			? calledFrom === "checkinSourceChart"
				? {
						...commonQueryParams,
						search_by: "error",
						search_value:
							i18n.global.messages.value[i18n.global.locale.value].common
								.success, //to access to the success value of common.ts file, located in locales/'language' folder, depending on the locale language
				  }
				: calledFrom === "errorCharts"
				? {
						...commonQueryParams,
						search_by: "error_code",
						search_value:
							config.w.config.series[0].data[config.dataPointIndex].x,
				  }
				: null
			: { ...commonQueryParams },
	});
};

const redirectFromComparisonToTable = (
	eventData: uiChartPointSelection,
	tableName: string,
	dateRange: DateRangeType,
	categories: string[],
): void => {
	const brandIndex = eventData.config.dataPointIndex;
	const selectedBrand = categories[brandIndex];

	const { days_before } = calculateDaysBefore(dateRange);

	const currentDateUTC = getCurrentDateInUTC()

	const dateTo = format(currentDateUTC, "yyyy-MM-dd HH:mm:ss");

	const dateFrom = format(
		subDays(currentDateUTC, days_before),
		"yyyy-MM-dd HH:mm:ss",
	);

	router.push({
		name: tableName,
		params: {
			brand_id: selectedBrand
		},
		query: {
					date_from: dateFrom,
					date_to: dateTo,
			  },
	});
};

interface ErrorCounts {
	[errorType: string]: number;
}

const calculateErrors = (data: StatsData[] | undefined) => {
	try {
		if (!data) {
			return [];
		}

		const errorCounts: ErrorCounts = {};

		data.forEach((item) => {
			const { error } = item;
			error.types.forEach((errorObj) => {
				const { error: errorType, total } = errorObj;

				if (errorCounts[errorType]) {
					errorCounts[errorType] += total;
				} else {
					errorCounts[errorType] = total;
				}
			});
		});

		const errorArray = Object.entries(errorCounts).map(([error, count]) => {
			return {
				x: getErrorMessage(error),
				y: count,
			};
		});

		errorArray.sort((a, b) => b.y - a.y);
		return [
			{
				name: "Error Count",
				data: errorArray,
			},
		];
	} catch (error) {
		console.error(error);
	}
};

export {
	makeSeries,
	formatSeries,
	makeOptions,
	calculateErrors,
	redirectToTable,
	redirectFromComparisonToTable,
};
