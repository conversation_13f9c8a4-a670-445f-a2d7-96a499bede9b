import type { ComparisonBrandRatio } from "@/types";
import type { getStatsValuesParams } from "@/uiTypes";
import { getStatValues } from "./uiStats";
import { describe, it, expect, beforeEach } from "vitest";
import { getPercentage, calculateSuccessRate } from "./uiStats";
import { DocumentMagnifyingGlassIcon } from "@heroicons/vue/24/outline";
import { t } from "@/locales";

describe("helpers/uiStats functions behaviour", () => {
	describe("getStatValues function", () => {
		let params: getStatsValuesParams;
		const explanationTranslations = {
			total: t("common.total"),
			of: t("uiStats.of"),
		};

		beforeEach(() => {
			params = {
				title: t("uiStats.title.reservation"),
				icon: DocumentMagnifyingGlassIcon,
				explanationTranslations,
			};
		});

		it("should return default uiStatValue when brands array is empty", () => {
			params.brands = [];

			const result = getStatValues(params);

			expect(result).toEqual({
				name: params.title,
				icon: params.icon,
				showFooter: false,
				changeType: "neutral",
				stat: "0%",
				change: "0%",
				explanation: "0 total",
			});
		});

		it("should calculate success rate and set stat and change accordingly", () => {
			params.brands = [
				{ brand_id: 1, total: 100, success: 70, error: 30 },
				{ brand_id: 2, total: 80, success: 60, error: 20 },
				{ brand_id: 3, total: 120, success: 90, error: 30 },
			];

			params.previousBrands = [
				{ brand_id: 1, total: 110, success: 80, error: 30 },
				{ brand_id: 2, total: 60, success: 50, error: 10 },
				{ brand_id: 3, total: 120, success: 100, error: 20 },
			];

			const result = getStatValues(params);

			expect(result).toEqual({
				name: params.title,
				icon: params.icon,
				showFooter: false,
				changeType: "decrease",
				stat: "73%",
				change: "8%",
				explanation: "220 of 300",
			});
		});

		it("should handle null and undefined 'success' values", () => {
			params.brands = [
				{ brand_id: 1, total: 100, success: null, error: undefined },
				{ brand_id: 2, total: 80, success: undefined, error: 20 },
				{ brand_id: 3, total: 120, success: 90, error: null },
			];

			params.previousBrands = [
				{ brand_id: 1, total: 80, success: null, error: undefined },
				{ brand_id: 2, total: 110, success: undefined, error: 20 },
				{ brand_id: 3, total: 90, success: 90, error: null },
			];

			const result = getStatValues(params);

			expect(result).toEqual({
				name: params.title,
				icon: params.icon,
				showFooter: false,
				changeType: "decrease",
				stat: "30%",
				change: "7%",
				explanation: "90 of 300",
			});
		});

		it('should handle a brand with zero "total" and null "success" and "error"', () => {
			params.brands = [
				{ brand_id: 1, total: 0, success: undefined, error: null },
				{ brand_id: 2, total: 0, success: null, error: null },
			];

			params.previousBrands = [
				{ brand_id: 1, total: 0, success: null, error: undefined },
				{ brand_id: 2, total: 0, success: undefined, error: 20 },
			];

			const result = getStatValues(params);

			expect(result).toEqual({
				name: params.title,
				icon: params.icon,
				showFooter: false,
				stat: "0%",
				change: "0%",
				changeType: "neutral",
				explanation: "0 total",
			});
		});
		it('should handle a brand with null/undefined "total" values and zeros "success"', () => {
			params.brands = [
				{ brand_id: 1, total: null, success: 0, error: null },
				{ brand_id: 2, total: undefined, success: 0, error: null },
			];

			params.previousBrands = [
				{ brand_id: 1, total: undefined, success: 0, error: undefined },
				{ brand_id: 2, total: null, success: 0, error: null },
			];

			const result = getStatValues(params);

			expect(result).toEqual({
				name: params.title,
				icon: params.icon,
				showFooter: false,
				stat: "0%",
				change: "0%",
				changeType: "neutral",
				explanation: "0 total",
			});
		});
	});

	describe("getPercentage function", () => {
		it("should return the correct percentage when data and totalData are positive integers", () => {
			const data = 5;
			const totalData = 10;
			const result = getPercentage(data, totalData);
			expect(result).toBe(50);
		});
		it("should return the correct percentage when data and totalData are positive integers rounding to floor", () => {
			const data = 3;
			const totalData = 27;
			const result = getPercentage(data, totalData);
			expect(result).toBe(11);
		});
		it("should return 0 when data is 0 and totalData is positive", () => {
			const data = 0;
			const totalData = 10;
			const result = getPercentage(data, totalData);
			expect(result).toBe(0);
		});
		it("should return 0 when totalData is 0", () => {
			const data = 5;
			const totalData = 0;
			const result = getPercentage(data, totalData);
			expect(result).toBe(0);
		});
		it("should return 0 when totalData is null", () => {
			const data = 1;
			const totalData = null;
			const result = getPercentage(data, totalData);
			expect(result).toBe(0);
		});
		it("should return 0 when data is negative and totalData is positive", () => {
			const data = -5;
			const totalData = 10;
			const result = getPercentage(data, totalData);
			expect(result).toBe(0);
		});
		it("should return 0 when data is positive and totalData is negative", () => {
			const data = 5;
			const totalData = -10;
			const result = getPercentage(data, totalData);
			expect(result).toBe(0);
		});
		it("should return 0 when data is negative and totalData is negative", () => {
			const data = -5;
			const totalData = -10;
			const result = getPercentage(data, totalData);
			expect(result).toBe(0);
		});
	});

	describe("calculateSuccessRate function", () => {
		it("should calculate success rate when all brand ratios have positive total and success values", () => {
			const data: Array<ComparisonBrandRatio> = [
				{ brand_id: 1, total: 10, success: 8, error: 2 },
				{ brand_id: 2, total: 15, success: 12, error: 3 },
				{ brand_id: 3, total: 20, success: 18, error: 2 },
			];
			const result = calculateSuccessRate(data);
			expect(result).toBe(84);
		});
		it("should calculate success rate when all brand ratios have zero success values", () => {
			const data: Array<ComparisonBrandRatio> = [
				{ brand_id: 1, total: 10, success: 0, error: 2 },
				{ brand_id: 2, total: 15, success: 0, error: 3 },
				{ brand_id: 3, total: 20, success: 0, error: 2 },
			];
			const result = calculateSuccessRate(data);
			expect(result).toBe(0);
		});
		it("should calculate success rate when all brand ratios have zero total values", () => {
			const data: Array<ComparisonBrandRatio> = [
				{ brand_id: 1, total: 0, success: 8, error: 2 },
				{ brand_id: 2, total: 0, success: 12, error: 3 },
				{ brand_id: 3, total: 0, success: 18, error: 2 },
			];
			const result = calculateSuccessRate(data);
			expect(result).toBe(0);
		});
		it("should calculate success rate when all brand ratios have zero total and success values", () => {
			const data: Array<ComparisonBrandRatio> = [
				{ brand_id: 1, total: 0, success: 0, error: 2 },
				{ brand_id: 2, total: 0, success: 0, error: 3 },
				{ brand_id: 3, total: 0, success: 0, error: 2 },
			];
			const result = calculateSuccessRate(data);
			expect(result).toBe(0);
		});
	});
});
