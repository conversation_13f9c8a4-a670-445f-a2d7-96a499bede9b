import { getComparisonRatioStats } from "@/modules/Comparison/Comparison";
import type {
	BrandRatioObject,
	UpdateDateRangeBrands,
	ComparisonBrandRatio,
} from "@/types";
import { defineStore } from "pinia";

const ACCEPTED_TYPES = ["reservation", "scan", "checkin"];
const EMPTY_BRAND_RATIO_ITEM = {
	brand_id: null,
	total: 0,
	success: 0,
	error: 0,
};

interface State {
	reservation: Array<ComparisonBrandRatio>;
	scan: Array<ComparisonBrandRatio>;
	checkin: Array<ComparisonBrandRatio>;
	previousReservation: Array<ComparisonBrandRatio>;
	previousScan: Array<ComparisonBrandRatio>;
	previousCheckin: Array<ComparisonBrandRatio>;
	error: string;
}

export const useComparisonStore = defineStore("comparison", {
	persist: true,
	state: (): State => {
		return {
			reservation: [],
			scan: [],
			checkin: [],
			previousReservation: [],
			previousScan: [],
			previousCheckin: [],
			error: "",
		};
	},
	actions: {
		async getComparisonStats({ brandIds, name, range }: UpdateDateRangeBrands) {
			try {
				if (name && !ACCEPTED_TYPES.includes(name)) {
					throw new Error(
						"getBrands action failed: => Error: Invalid 'name' param",
					);
				}
				if (name) {
					this[name] = [];
				} else {
					this.reset();
				}

				const response = await getComparisonRatioStats({
					brandIds,
					name,
					range,
				}) as { current: BrandRatioObject, previous: BrandRatioObject };

				if (name && ACCEPTED_TYPES.includes(name)) {
					this.setResponse(response.current[name], name);
					this.setPreviousPeriodResponse(response.previous[name], name);
				} else {
					this.setResponse(response.current);
					this.setPreviousPeriodResponse(response.previous);
				}

			} catch (error) {
				if (error) {
					this.error = error.message;
				}
				console.error(error);
			}
		},
		reset() {
			this.reservation = [];
			this.scan = [];
			this.checkin = [];
			this.previousReservation = [];
			this.previousScan = [];
			this.previousCheckin = [];
			this.error = "";
		},
		setResponse(
			response: Array<ComparisonBrandRatio | undefined> | BrandRatioObject,
			type?: keyof BrandRatioObject | undefined,
		) {
			if (!type) {
				for (const typeName of ACCEPTED_TYPES) {
					this[typeName] = response[typeName].length
						? response[typeName]
						: [EMPTY_BRAND_RATIO_ITEM];
				}
			} else {
				this[type] = response.length ? response : [EMPTY_BRAND_RATIO_ITEM];
			}
		},
		setPreviousPeriodResponse(
			response: Array<ComparisonBrandRatio | undefined> | BrandRatioObject,
			type?: keyof BrandRatioObject | undefined,
		) {
			if (!type) {
				for (const typeName of ACCEPTED_TYPES) {
					this[
						`previous${typeName.charAt(0).toUpperCase() + typeName.slice(1)}`
					] = response[typeName].length
						? response[typeName]
						: [EMPTY_BRAND_RATIO_ITEM];
				}
			} else {
				this[`previous${type.charAt(0).toUpperCase() + type.slice(1)}`] =
					response.length ? response : [EMPTY_BRAND_RATIO_ITEM];
			}
		},
	},
});
