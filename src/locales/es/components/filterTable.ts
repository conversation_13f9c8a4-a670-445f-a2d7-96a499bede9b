export default {
	filterBy: "Filtrar por",
	createButton: "Crear usuario",
	filterButton: "Filtrar",
	exportButton: "Exportar Resultados",
	sessionId: "ID de Sesión",
	date: "Fecha",
	result: "Resultado",
	errorCode: "Código de Error",
	source: {
		title: "Fuente",
		types: {
			autocheckin: "Autocheckin",
			reception: "Recepción",
		},
	},
	roomNumber: "Número de habitación",
	reservationCode: "Localizador",
	reservationId: "ID de Reserva",
	userId: "ID de Usuario",
	firstName: "Nombre",
	lastName: "Apellido",
	checkIn: "Check in",
	checkOut: "Check out",
	side: "Lado",
	nationality: "Nacionalidad",
	documentNumber: "Número de Documento",
	document_type: {
		title: "Tipo de documento",
		types: {
			identity_card: "Documento de identidad",
			passport: "Pasaporte",
			driving_license: "Carnet de conducir",
			residence_permit: "Permiso de residencia",
		},
	},
	showing: "Mostrando",
	of: "de",
	next: "Siguiente",
	previous: "Anterior",
	noResults: {
		title: "No se han encontrado resultados",
		body: "Por favor intenta alguna de las siguientes acciones",
		back: "Volver atrás",
		home: "Volver al inicio",
	},
	delete: "borrar",
	manual: "Manual",
	brandIds: "ID de brands",
	group: "Grupo",
	tableHeaderCreatedAt: "Creado en",
	tableHeaderUpdatedAt: "Actualizado en",

	mfa: "MFA",
	breadCrumb: {
		selector: "Gestión de usuarios",
	},
};
