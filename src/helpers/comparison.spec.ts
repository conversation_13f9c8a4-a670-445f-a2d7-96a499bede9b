import { describe, it, expect } from "vitest";
import {
	calculateHeight,
} from "./comparison";

describe("helpers/comparison functions behaviour", () => {
	//calculateHeight function
	it("should return 250 when input is 0", () => {
		expect(calculateHeight(0)).toBe(250);
	});
	it("should return 250 when input is less than 8", () => {
		expect(calculateHeight(7)).toBe(250);
		expect(calculateHeight(1)).toBe(250);
	});
	it("should return 500 when input is between 8 and 15", () => {
		expect(calculateHeight(8)).toBe(400);
		expect(calculateHeight(14)).toBe(400);
	});
	it("should return 750 when input is between 15 and 20", () => {
		expect(calculateHeight(15)).toBe(600);
		expect(calculateHeight(19)).toBe(600);
	});
	it("should return correct calc when input is after 20", () => {
		expect(calculateHeight(20)).toBe(660);
		expect(calculateHeight(43)).toBe(1189);
	});
	it("should return 250 when input is negative", () => {
		expect(calculateHeight(-5)).toBe(250);
		expect(calculateHeight(-10)).toBe(250);
		expect(calculateHeight(-15)).toBe(250);
	});
});
