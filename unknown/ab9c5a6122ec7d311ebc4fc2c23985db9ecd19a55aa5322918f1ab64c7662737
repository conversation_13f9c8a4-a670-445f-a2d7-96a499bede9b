import { defineStore } from "pinia";
import { Auth } from "aws-amplify";
import { CognitoUser } from "@aws-amplify/auth";
import { login, confirmMfa } from "../modules/Auth/Auth";
import type { AuthError } from "../modules/Auth/Auth";
interface userData {
	email: string;
	password: string;
	code?: string;
}

type ErrorState = "" | AuthError;

interface State {
	email: string;
	isAuthenticated: boolean;
	needMfaSetup: boolean;
	user: CognitoUser | {};
	error: ErrorState;
	redirectUrl: string;
	brandsAllowed: number[];
	currentRoles: string[];
}

export const useUserStore = defineStore("user", {
	persist: true,
	state: (): State => {
		return {
			email: "",
			isAuthenticated: false,
			needMfaSetup: false,
			user: {},
			error: "",
			redirectUrl: "/",
			brandsAllowed: [],
			currentRoles: [],
		};
	},
	getters: {
		isAllowed: (state) => {
			return (brand_id: number): boolean => {
				return (
					(state.isAuthenticated && state.brandsAllowed.includes(brand_id)) ||
					state.currentRoles.includes("admin")
				);
			};
		},
		isAdmin: (state) => {
			return state.currentRoles.includes("admin");
		},
		isReception: (state) => {
			return state.currentRoles.includes("reception");
		},
	},
	actions: {
		setEmail(email: string): void {
			this.email = email;
		},

		async load(): Promise<CognitoUser | null> {
			try {
				const user = await Auth.currentAuthenticatedUser();

				this.user = user;
				this.isAuthenticated = true;
				this.error = "";
				return Promise.resolve(user);
			} catch (error) {
				this.user = {};
				console.warn(error);
				return Promise.resolve(null);
			}
		},

		async login(userData: userData) {
			try {
				const user = await login(userData.email, userData.password);
				const mfaRequired = user?.attributes["custom:mfa_required"];

				this.needMfaSetup =
					mfaRequired === "true" && user?.preferredMFA === "NOMFA";

				return this.formatUserData(user, !this.needMfaSetup);
			} catch (error) {
				if (error.code === "NewPasswordRequiredException") {
					this.user = error.user;
				}
				if (error.code === "SoftwareTokenMfaException") {
					this.user = error.user;
				}
				this.error = error.code || "defaultError";
			}
		},

		async validateMfaCode(code: string) {
			try {
				const user = await confirmMfa(this.user, code);
				return this.formatUserData(user);
			} catch (error) {
				this.error = error.code || "defaultError";
			}
		},

		formatUserData(user, isAuthenticated = true) {
			if (!user?.roles) {
				throw new Error();
			}
			this.brandsAllowed = user?.brands ?? this.brandsAllowed;
			this.currentRoles = user?.roles ?? this.currentRoles;
			this.user = user;
			this.isAuthenticated = isAuthenticated;
			this.error = "";
			return Promise.resolve(this.user);
		},

		async completeNewPassword(password: string) {
			try {
				await Auth.completeNewPassword(this.user, password);
				await this.login({ email: this.email, password: password });
			} catch (error) {
				this.error = error.code;
			}
		},

		async logout() {
			return await Auth.signOut()
				.then((data) => {
					this.isAuthenticated = false;
					this.needMfaSetup = false;
					this.user = {};
					this.brandsAllowed = [];
					this.error = "";
					this.currentRoles = [];
					return Promise.resolve(data);
				})
				.catch((error) => {
					this.error = error.code;
				});
		},
		async forgotPassword(username: string) {
			return await Auth.forgotPassword(username)
				.then((data) => {
					this.error = "";
					return Promise.resolve(data);
				})
				.catch((error) => {
					this.error = error.code;
				});
		},

		async forgotPasswordSubmit(userData: userData) {
			return await Auth.forgotPasswordSubmit(
				userData.email,
				userData.code || "",
				userData.password,
			)
				.then((data) => {
					this.error = "";
					return Promise.resolve(data);
				})
				.catch((error) => {
					this.error = error.code;
				});
		},

		async setupMfa() {
			try {
				const code = await Auth.setupTOTP(this.user);
				const otpAuth = `otpauth://totp/${this.user?.attributes?.email}?secret=${code}&issuer=Hotelinking Autocheckin`;

				return { code, otpAuth };
			} catch (err) {
				console.error("Error on setupMfa", err);
				throw err;
			}
		},

		async verifyMfa(token: string) {
			try {
				await Auth.verifyTotpToken(this.user, token);
				await Auth.setPreferredMFA(this.user, "TOTP");
				return true;
			} catch (e) {
				console.error("Error verifying software token");
				return false;
			}
		},
	},
});
