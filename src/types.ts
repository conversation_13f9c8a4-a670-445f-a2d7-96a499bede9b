interface ErrorType {
	error: string;
	total: number;
}

export interface ErrorProperty {
	count: number;
	types: ErrorType[];
}

export interface StatsData {
	time: string;
	total: number;
	success: number;
	error: ErrorProperty;
}

export interface CheckinSourceData {
	time: string;
	autocheckin: string;
	reception: string;
	total: number;
}

export interface FunnelData {
	page: string;
	total: number;
}

export type BrandInfo = {
	name: string;
	logo: string;
	id: number;
	time_zone: string;
};

export type AccountInfo = {
	name: string;
	logo: string;
	background_color: string | null;
	id: number;
};

type BrandConfig = {
	partial_checkin: boolean;
	room_type_selection: boolean;
	time_limit_checkin: number;
	activate_time_limit: boolean;
	disable_address_autocomplete: boolean;
	reservation_holder_not_modifiable: boolean;
	max_attempts_reservation: number;
	max_attempts_child: number;
	show_holder: boolean;
	show_modal_in_confirmation_page: boolean;
	optional_scan: boolean;
	send_identity_documents_to_PMS: boolean;
	identification: Identification;
	advanced_scan: boolean;
	custom_confirmation_text: boolean;
	max_attempts_telephone: number;
	telephone_notifications: boolean;
	show_send_newsletter_checkbox: boolean;
	close_time_limit_checkin: number;
	send_email_checkin_available: boolean;
	max_attempts_document: number;
	redirect_link: boolean;
	scan_children_like_adults: boolean;
	comments: boolean;
	show_save_phone_in_database_checkbox: boolean;
	show_comments_only_on_holder: boolean;
	active: boolean;
	custom_phone_text: boolean;
	telephone: boolean;
	token_key: string;
	custom_scan_text: boolean;
	signed_documents: boolean;
	child_required_identity_documents_age: number;
	children_sign_documents: boolean;
	send_signed_documents_to_reception: boolean;
	allow_expired_documents: boolean;
	custom_gdpr_text: boolean;
	show_qr_code: boolean;
	reception_signature: boolean;
	paymentsActive: number;
	send_identity_documents_to_reception: boolean;
};

export type Brand = {
	active: boolean;
	brandInfo: BrandInfo;
	accountInfo?: AccountInfo;
	config: BrandConfig;
};

type ReservationFilter = {
	name: string;
	type: ReservationFilterType;
	position: number;
};
type ReservationInput = {
	name: string;
	active: string;
	position: string;
	type: ReservationFilterType;
	maxLength?: string;
	minLength?: string;
};
type ChildForm = {
	name: string;
	active: string;
	type: string;
	required: string;
	maxLength?: string;
	minLength?: string;
	countryInput?: string;
	options?: string[];
	fill_from_holder?: string;
	position?: number;
};

type Identification = {
	child_form: Array<ChildForm[]>;
	reservation_filters: Array<ReservationFilter[]>;
	reservation_inputs: Array<ReservationInput[]>;
	validate_data_scan: Array<ChildForm[]>;
};

export enum ReservationFilterType {
	Date = "date",
	Email = "email",
	Text = "text",
}

export interface ApiResponse {
	search_funnel?: FunnelData[];
	guest_funnel?: FunnelData[];
	preCheck?: StatsData[];
	checkinSource?: CheckinSourceData[];
	current: {
		[currentKey: string]: StatsData[];
	};
	previous: {
		[previousKey: string]: StatsData[];
	};
}

//types for Reservations
export interface ReservationParams {
	reservation_code?: string;
	first_name?: string;
	last_name?: string;
	check_in?: string;
	check_out?: string;
	email?: string;
	manual?: boolean;
	room_number?: string;
}

interface CheckinGuest {
	name: string;
	surname: string;
	second_surname?: string;
	nationality: string;
}

export interface CheckinParams {
	res_id: string;
	check_in: string;
	check_out: string;
	guests: CheckinGuest[];
}

export interface ScanParams {
	allowExpiredDocuments: boolean;
	check_in: string;
	check_out: string;
	country: string;
	image: string;
	res_id: string;
	room_number?: string;
}

interface Address {
	house_number: string | null;
	postcode: string | null;
	province: string | null;
	street_name: string | null;
}

export interface ScanObject {
	data: {
		address: Address;
		birthday_date: string | null;
		date_of_expiry: string | null;
		date_of_issue: string | null;
		document_number: string | null;
		document_subtype: string | null;
		document_support_number: string | null;
		document_type: string | null;
		face: string | null;
		gender: string | null;
		issuing_country: string | null;
		issuing_institution: string | null;
		name: string | null;
		nationality: string | null;
		place_of_birth: string | null;
		residence_country: string | null;
		second_surname: string | null;
		side: string | null;
		signature: string | null;
		surname: string | null;
	};
}

export interface ErrorResponse {
	error: {
		code: string;
		message?: string;
	};
}

export interface ReservationSuccessResponse {
	brand_id: string;
	check_in: string;
	check_out: string;
	res_id: string;
	res_localizer: string;
	holder: object;
}
export interface DataItem {
	trace_id: string;
	source: string;
	time: string;
	error: string;
	error_code: string | null;
	name: string;
}

export interface ReservationData extends DataItem {
	params: ReservationParams;
	response: ErrorResponse | ReservationSuccessResponse;
}

export interface CheckinData extends DataItem {
	params: CheckinParams;
	response: ErrorResponse | string;
}

export interface ScanData extends DataItem {
	params: ScanParams;
	response: ErrorResponse | ScanObject;
}

export interface Meta {
	current_page: number;
	from: number;
	last_page: number;
	path: string;
	per_page: number;
	to: number;
	total: number;
}

export interface ApiRequestResponse {
	data: ReservationData[] | CheckinData[] | ScanData[] | UserData[];
	meta: Meta;
}
export interface ApiReservationsResponse extends ApiRequestResponse {
	data: ReservationData[];
}

export interface FormattedData {
	id: number;
	row: (string | ErrorObject | ErrorMessageObject)[];
}

export interface ErrorObject {
	content: string;
	color: string;
	type: string;
}

export interface ErrorMessageObject {
	content: string;
	type: string;
	emits: string;
	message: string;
}

export interface SearchFilters {
	date_from: string;
	date_to: string;
	search_by: string;
	search_value: string | null;
	sort_field: string;
	sort_order: string;
	page: number;
	per_page: number;
	name?: string;
}

export interface TableWrapperFilters {
	name: string;
	value: string;
	active?: boolean;
}
// TODO: change table object to the correct format
export interface TableWrapperProps {
	filters: TableWrapperFilters[];
	content?: string;
	last_page: number | null;
	items: FormattedData[] | null;
	header: { name: string; value: string }[];
	ordered_by: string;
	order_direction: string;
	checkbox?: boolean;
	createButtonIcon?: any;
	createButtonText?: string;
}

export type DateRangeType = "24h" | "7d" | "1m" | "3m" | "" | AbsoluteDateRange;

export type AbsoluteDateRange = { from: string; to: string };

export interface GetOverviewParams {
	brand_id: number;
	isReceptionModeEnabled?: boolean;
	range: DateRangeType;
	name?: string;
}

interface UpdateDateRange {
	name?: OverviewChartName;
	range?: "24h" | "7d" | "1m" | { from: string; to: string }; 
	isReceptionModeEnabled?: boolean;
}

export interface UpdateDateRangeBrands extends UpdateDateRange {
	brandIds: Array<number>;
}
export type ErrorMappingKeys =
	| "scan_errors"
	| "reservation_errors"
	| "checkin_errors"
	| "checkinSource"
	| "reservation"
	| "scan"
	| "checkin";

export type OverviewChartName =
	| "reservation"
	| "scan"
	| "checkin"
	| "search_funnel"
	| "guest_funnel"
	| "reservation_errors"
	| "scan_errors"
	| "checkin_errors"
	| "checkinSource";

export type BrandRatioObject = {
	reservation?: Array<ComparisonBrandRatio | undefined>;
	checkin?: Array<ComparisonBrandRatio | undefined>;
	scan?: Array<ComparisonBrandRatio | undefined>;
};

export interface BrandRatio {
	total: number;
	success: number;
	error: number | ErrorProperty;
}

export interface ComparisonBrandRatio extends BrandRatio {
	brand_id: number;
}

export interface ComparisonApiResponse {
	current: BrandRatioObject;
	previous: BrandRatioObject;
}
export interface OverviewBrandRatio extends BrandRatio {
	time: string;
}

export type SerieData = {
	name: string;
	data: Array<number>;
};

export interface UserData {
	id: string;
	email: string;
	brand_ids: string;
	group: string;
	created_at: string;
	updated_at: string;
	[key: string]: string;
}

export interface CreateUserData {
	email: string;
	brandId: string;
	group: string;
	mfa: boolean;
}

export interface GroupRequestParams {
	GroupName: string;
	UserPoolId: string;
	Limit: number;
	NextToken?: string;
}

export interface Group {
	CreationDate: string;
	Description: string;
	GroupName: string;
	LastModifiedDate: string;
	RoleArn: string;
	UserPoolId: string;
}

export interface GroupResponse {
	$metadata: {
		httpStatusCode: number;
		requestId: string;
		attempts: number;
		totalRetryDelay: number;
	};
	Groups: Group[];
}

export interface Attributes {
	Name: string;
	Value: string;
}

export interface SelectItem {
	name: string;
	id: string;
}

export interface TableActionEvent {
	action: string;
	items: number[];
}

export interface EditUserModalAction {
	modal: "editUsersModal";
	action: "edit" | "close";
	selectedBrands: string;
	selectedGroup: { name: string; id: string };
	selectedMfaRequired: boolean;
	usersIndex: number[];
}

export interface Column {
	name: string;
	value: string;
}
