import type {
	uiStatsItem,
	getStatsValuesParams,
	uiStatsChangeType,
} from "@/uiTypes";
import type { OverviewBrandRatio, ComparisonBrandRatio } from "@/types";
import { sumBy } from "lodash-es";

export const getStatValues = ({
	brands,
	previousBrands,
	title,
	icon,
	actionText,
	explanationTranslations,
	showFooter = false,
}: getStatsValuesParams): uiStatsItem => {
	const currentSuccessRate = brands ? calculateSuccessRate(brands) : 0;
	const prevSuccessRate = previousBrands
		? calculateSuccessRate(previousBrands)
		: 0;
	let successRateDifference = 0;

	if (prevSuccessRate !== 0) {
		successRateDifference = Math.floor(
			((currentSuccessRate - prevSuccessRate) / prevSuccessRate) * 100,
		);
	} else {
		successRateDifference = Math.floor(currentSuccessRate * 100);
	}

	let newChangeType: uiStatsChangeType = "neutral";
	if (successRateDifference > 0) {
		newChangeType = "increase";
	} else if (successRateDifference < 0) {
		newChangeType = "decrease";
	}

	const successCount = brands ? calculateSuccessCount(brands) : 0;

	const explanation =
		successCount && brands
			? `${calculateSuccessCount(brands)} ${
					explanationTranslations.of
			  } ${calculateTotalCount(brands)}`
			: `0 ${explanationTranslations.total}`;

	return {
		name: title,
		icon,
		showFooter,
		changeType: newChangeType,
		stat: brands ? `${calculateSuccessRate(brands)}%` : "0%",
		change: successRateDifference
			? `${Math.abs(successRateDifference)}%`
			: "0%",
		...(actionText && { actionText }),
		explanation: explanation,
	};
};

const calculateSuccessCount = (
	data: Array<OverviewBrandRatio | ComparisonBrandRatio>,
): number => {
	return sumBy(data, (item) => {
		if ("success" in item) {
			return item.success || 0;
		} else if ("autocheckin" in item) {
			return item.autocheckin || 0;
		}
		return 0;
	});
};

const calculateTotalCount = (
	data: Array<OverviewBrandRatio | ComparisonBrandRatio>,
): number => {
	return sumBy(data, (item) => item.total || 0);
};

export const calculateSuccessRate = (
	data: Array<OverviewBrandRatio | ComparisonBrandRatio>,
): number => {
	const totalCount = calculateTotalCount(data);
	const successCount = calculateSuccessCount(data);
	const percentage = getPercentage(successCount, totalCount);

	return percentage;
};

export const getPercentage = (data: number, totalData: number): number => {
	return totalData > 0 && data > 0 ? Math.floor((data / totalData) * 100) : 0;
};
