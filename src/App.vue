<script setup lang="ts">
import { useNotificationStore } from "@/stores/notification";
import { useLoadingStore } from "@/stores/loading";
import { computed } from "vue";

const loadingStore = useLoadingStore();
const isLoading = computed(() => loadingStore.loading);

const notificationStore = useNotificationStore();
</script>

<template>
  <uiLoadingScreen v-if="isLoading" />
  <uiNotification
    class="notification z-50"
    :show="notificationStore.show"
    :type="notificationStore.type"
    :title="notificationStore.title"
    :message="notificationStore.message"
    @close-notification="notificationStore.clear()"
  />
  <RouterView />
</template>

<style scoped></style>
