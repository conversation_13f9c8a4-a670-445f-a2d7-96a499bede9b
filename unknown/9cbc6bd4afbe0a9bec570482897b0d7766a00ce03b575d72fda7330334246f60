import { defineStore } from "pinia";
import RequestRepository from "@/modules/Request/request";
import type { SearchFilters, ReservationData } from "@/types";

interface State {
	items: ReservationData[] | null;
	last_page: number | null;
}

export const useReservationStore = defineStore("reservation", {
	state: (): State => {
		return {
			items: null,
			last_page: null,
		};
	},
	actions: {
		async getReservations(brandId: number, params: SearchFilters) {
			try {
				this.items = null;
				this.last_page = null;

				const response = await RequestRepository.get(brandId, {
					name: "reservation",
					...params,
				});

				this.items = response?.data ?? [];
				this.last_page = response.meta.last_page;

				return Promise.resolve();
			} catch (error) {
				console.error("Failed to fetch reservations", error);
				return Promise.reject();
			}
		},
	},
});
