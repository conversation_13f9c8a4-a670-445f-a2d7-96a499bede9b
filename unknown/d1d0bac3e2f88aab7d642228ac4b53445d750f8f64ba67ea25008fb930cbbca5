import { i18n } from "@/locales";
// import getBrandsMock from "../../mocks/getBrands";
import { useBrandStore } from "@/stores/brand";
import AccountsView from "@/views/AccountsView.vue";
import { mount } from "@vue/test-utils";
import { createPinia, setActivePinia } from "pinia";
import {
	afterEach,
	beforeAll,
	beforeEach,
	describe,
	expect,
	it,
	vi,
} from "vitest";
import { ref } from "vue";
// import { useRouter } from "vue-router";

describe("AccountsView unit tests", () => {
	beforeAll(() => {
		setActivePinia(createPinia());
	});

	beforeEach(async () => {
		vi.mock("@vueuse/router", () => ({
			useRouteQuery: vi.fn(() => ref()),
		}));

		vi.mock("vue-router", () => ({
			useRoute: vi.fn().mockReturnValue({
				params: {},
			}),
			useRouter: vi.fn(() => ({
				currentRoute: ref({
					name: "/brands",
				}),
				push: (to: Object) => vi.fn(),
			})),
		}));
		const store = useBrandStore();
		store.get = vi.fn();
		store.filterBrands = vi.fn();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it("should render skeletons before gettings data, then only render data", async () => {
		const wrapper = mount(AccountsView, {
			global: {
				plugins: [i18n],
			},
		});

		expect(
			wrapper.find('[data-test="brands-skeleton-cards"]').isVisible(),
		).toBeTruthy();
		expect(wrapper.find('[data-test="brands-cards"]').exists()).toBeFalsy();

		// await flushPromises();

		// expect(
		// 	wrapper.find('[data-test="brands-skeleton-cards"]').isVisible(),
		// ).toBeFalsy();
		// expect(wrapper.find('[data-test="brands-cards"]').exists()).toBeTruthy();

		// expect(wrapper.findAll('[data-test="brands-cards"]').length).toBe(
		// 	getBrandsMock.data.length,
		// );
	});

	it("should redirect to overview route with brand id when click on card", async () => {
		//Once store.filterBrands is fix this test works till expect method
		// const wrapper = mount(AccountsView, {
		// 	global: {
		// 		plugins: [i18n],
		// 	},
		// });
		// // const spy = vi.spyOn(useRouter(), "push");
		// await flushPromises();
		// const firstCard = wrapper.findAll('[data-test="brands-cards"]')[0];
		// await firstCard.trigger("click");
		// const expectedPushParams = {
		// 	name: "overview",
		// 	params: {
		// 		brand_id: "76",
		// 	},
		// };
		// expect(spy).toBeCalled();
		// expect(spy).toHaveBeenCalledWith(expectedPushParams);
	});
});
