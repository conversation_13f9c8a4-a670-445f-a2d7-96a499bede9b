import { faker } from "@faker-js/faker";
import { add, format, eachDayOfInterval } from "date-fns";
import type { StatsData, ApiResponse } from "@/types";

function generateConsecutiveDates(
	startDate: Date,
	numberOfDays: number,
): string[] {
	const consecutiveDates = eachDayOfInterval({
		start: startDate,
		end: add(startDate, { days: numberOfDays }),
	});

	const formattedDates = consecutiveDates.map((date) =>
		format(date, "yyyy-MM-dd HH:mm:ss.SSSSSSSSS"),
	);
	return formattedDates;
}

function generateStats(
	consecutiveDates: string[],
	numberOfDays: number,
): StatsData[] {
	return Array.from({ length: numberOfDays }).map((_, i) => {
		const errorType1 = faker.number.int({ max: 20 });
		const errorType2 = faker.number.int({ max: 20 });
		const error = errorType1 + errorType2;
		const success = faker.number.int({ min: 20, max: 100 });
		const total = success + error;

		return {
			time: consecutiveDates[i],
			total: total,
			success: success,
			error: {
				count: error,
				types: [
					{ error: "INT_1_8", total: errorType1 },
					{ error: "INT_4_1", total: errorType2 },
				],
			},
		};
	});
}

export function getOverviewMock(): ApiResponse {
	const numberOfDays = faker.number.int({ min: 2, max: 30 });
	const randomDate = faker.date.recent();
	const consecutiveDates = generateConsecutiveDates(randomDate, numberOfDays);
	const reservationStats = generateStats(consecutiveDates, numberOfDays);
	const scanStats = generateStats(consecutiveDates, numberOfDays);
	const mockResponse = {
		data: {
			reservation: reservationStats,
			scan: scanStats,
			funnel: [
				{ page: "GDPR", total: faker.number.int({ min: 100, max: 500 }) },
				{ page: "search", total: faker.number.int({ min: 100, max: 500 }) },
				{
					page: "reservations",
					total: faker.number.int({ min: 100, max: 500 }),
				},
			],
		},
	};
	return mockResponse;
}
