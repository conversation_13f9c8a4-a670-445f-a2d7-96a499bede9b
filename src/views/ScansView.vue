<template>
  <TableWrapper
    :header="header"
    :ordered_by="table.orderedBy"
    :order_direction="table.orderDirection"
    :items="formattedScans"
    :filters="table.filters"
    :last_page="last_page"
    @filters-updated="getScans"
    @home="handleNoResults('home')"
  >
  </TableWrapper>
</template>

<script setup lang="ts">
import TableWrapper from "@/components/TableWrapper.vue";
import { storeToRefs } from "pinia";
import { useScanStore } from "@/stores/scan";
import { useUserStore } from "@/stores/user";
import { useBrandStore } from "@/stores/brand";
import { useRoute } from "vue-router";
import type {
  SearchFilters,
  FormattedData,
  ErrorObject,
  ErrorMessageObject,
  Column
} from "@/types";
import { t } from "@/locales";
import { computed, ref, watch} from "vue";
import { formatDataExtended, removeEmptyReservationsValues, filterEmptyFields} from "@/helpers/uiTable";
import router from "@/router";

const route = useRoute();

const brandId = ref(Number(route.params.brand_id));
const brandStore = useBrandStore();
const { getBrandById } = storeToRefs(brandStore);

const scanStore = useScanStore();
const userStore = useUserStore();
const { last_page } = storeToRefs(scanStore); // update local ref when table wrapper emits the table-updated

const formattedScans = ref<FormattedData[] | null>(null);
const columnIndicesToRemove = ref<number[]>([]);

const getScans = async (filters: SearchFilters) => {
  if (userStore.isAllowed(brandId.value)) {
    await scanStore.getScans(brandId.value, filters);
  }
};

const brandTimeZone = computed(() => {
   return getBrandById.value(brandId.value)?.time_zone; 
});

watch(
  () => scanStore.items, (newItems) => {
    if (!newItems) {
      formattedScans.value = null  
      columnIndicesToRemove.value = []
      return 
    }

    const allRows = newItems.map((scan, i) => {
      const { trace_id, time, error, params, response, source } = scan;
      const { formattedTime, errorTag, errorMessage, sourceTranslation } = formatDataExtended(time, error, source, response, brandTimeZone.value);

      const dataToUse = response?.error ? response?.error?.data : response?.data;
      const documentNumberSanitized = dataToUse?.document_type
        ? t(`filterTable.document_type.types.${dataToUse?.document_type}`)
        : "-";

      const newRow: (string | ErrorObject | ErrorMessageObject)[] = [
        trace_id,
        formattedTime,
        errorTag,
        errorMessage,
        sourceTranslation,
        params.room_number || "-",
        params.res_id || "-",
        params.check_in || "-",
        params.check_out || "-",
        dataToUse?.side || "-",
        dataToUse?.name || "-",
        dataToUse?.surname || "-",
        dataToUse?.nationality || "-",
        dataToUse?.document_number || "-",
        documentNumberSanitized || "-",
      ];

      return {
        id: i + 1,
        row: newRow,
      };
    });

    const { filteredRows, columnIndicesToRemove: indicesToRemove } = removeEmptyReservationsValues(allRows);

    formattedScans.value = filteredRows;
    columnIndicesToRemove.value = indicesToRemove; 
  },
);


const handleNoResults = (action: string) => {
  if (action === "home") {
    router.push({ name: "overview" });
  }
};

const header = computed((): Column[]  => {
  const columns = [
    { name: t("filterTable.sessionId"), value: "trace_id" },
    { name: t("filterTable.date"), value: "time" },
    { name: t("filterTable.result"), value: "error" },
    { name: t("filterTable.errorCode"), value: "error_code" },
    { name: t("filterTable.source.title"), value: "source" },
    { name: t("filterTable.roomNumber"), value: "" },
    { name: t("filterTable.reservationId"), value: "" },
    { name: t("filterTable.checkIn"), value: "" },
    { name: t("filterTable.checkOut"), value: "" },
    { name: t("filterTable.side"), value: "" },
    { name: t("filterTable.firstName"), value: "" },
    { name: t("filterTable.lastName"), value: "" },
    { name: t("filterTable.nationality"), value: "" },
    { name: t("filterTable.documentNumber"), value: "" },
    {name: t("filterTable.document_type.title"),value: "" },
  ];

  return filterEmptyFields<Column>(columns, columnIndicesToRemove.value)
});


const table = {
  orderedBy: "time",
  orderDirection: "desc",
  filters: [
    { name: t("filterTable.sessionId"), value: "trace_id" },
    { name: t("filterTable.result"), value: "error" },
    { name: t("filterTable.errorCode"), value: "error_code" },
    { name: t("filterTable.source.title"), value: "source" },
    { name: t("filterTable.roomNumber"), value: "room_number" },
    { name: t("filterTable.reservationId"), value: "res_id" },
    { name: t("filterTable.checkIn"), value: "check_in" },
    { name: t("filterTable.checkOut"), value: "check_out" },
    { name: t("filterTable.side"), value: "side" },
    { name: t("filterTable.firstName"), value: "name" },
    { name: t("filterTable.lastName"), value: "surname" },
    { name: t("filterTable.nationality"), value: "nationality" },
    { name: t("filterTable.documentNumber"), value: "document_number" },
    {
      name: t("filterTable.document_type.title"),
      value: "document_type",
    },
  ],
};
</script>
