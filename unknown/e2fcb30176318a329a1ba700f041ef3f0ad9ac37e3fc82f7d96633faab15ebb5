import type { DateRangeType, AbsoluteDateRange } from "@/types";
import {
	addDays,
	differenceInDays,
	addHours,
	differenceInHours,
	compareAsc,
} from "date-fns";
import { isAbsoluteDateRange } from "@/helpers/uiDateRange";

const validateDateRange = (range: AbsoluteDateRange): boolean => {
	const currentDate = new Date();
	const from = new Date(range.from);
	const to = new Date(range.to);

	// if any date is a future date
	if (from > currentDate || to > currentDate) {
		return false;
	}
	//returns 1 if the first date is after the second
	const isValid = compareAsc(from, to) < 1;

	return isValid;
};

const calculateDaysBefore = (range: DateRangeType) => {
	if (isAbsoluteDateRange(range)) {
		const fromObject = new Date(range.from);
		const toObject = new Date(range.to);
		//in case of absolute date range selected, calculate number of days included in the range
		return {
			days_before: getTimeDifference(toObject, fromObject, "1d"),
			interval: "1d",
		};
	} else {
		switch (range) {
			case "24h":
				return { days_before: 1, interval: "1h" };
			case "7d":
				return { days_before: 7, interval: "1d" };
			case "1m":
				return { days_before: 30, interval: "1d" };
			case "3m":
				return { days_before: 90, interval: "1d" };
			default:
				return { days_before: 1, interval: "1h" };
		}
	}
};

const getTimeDifference = (currentDate, previousDate, unit) => {
	if (unit === "1d") return differenceInDays(currentDate, previousDate);
	return differenceInHours(currentDate, previousDate);
};

const getNextDate = (previousDate, interval) => {
	return interval === "1d"
		? addDays(previousDate, 1)
		: addHours(previousDate, 1);
};

const getCurrentDateInUTC = () => {
	const currentDate = new Date();

	const currentDateUTC = new Date(
		currentDate.getUTCFullYear(),
		currentDate.getUTCMonth(),
		currentDate.getUTCDate(),
		currentDate.getUTCHours(),
		currentDate.getUTCMinutes(),
		currentDate.getUTCSeconds(),
	);

	return currentDateUTC;
};

export {
	calculateDaysBefore,
	getTimeDifference,
	getNextDate,
	getCurrentDateInUTC,
	validateDateRange,
};
