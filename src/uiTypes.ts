import type {
	BrandRatioObject,
	DateRangeType,
	OverviewBrandRatio,
	ComparisonBrandRatio,
} from "@/types";
import type { FunctionalComponent, HTMLAttributes, VNodeProps } from "vue";

export interface uiInputEventChange {
	name: string;
	value: string;
}

export interface uiChartEventData {
	range: DateRangeType;
	chartId: string;
}

export interface uiChartPointSelection {
	config: any;
	event: MouseEvent;
	id: string;
	chartContext: any;
}

export interface uiChartSelectedRangeEvent {
	chartId: keyof BrandRatioObject;
	range: DateRangeType;
}

export interface uiModalEvent {
	action: string;
	modal: string;
}

export interface uiCheckBoxItem {
	id: string;
	check: boolean;
	name: string;
}

export interface uiCheckBoxEvent {
	checked: boolean;
	value: string;
}

export type uiChartGetSerieParams = {
	successData: Array<number>;
	errorData: Array<number>;
};

export type uiStatsType = "reservation" | "scan" | "checkin";

export type uiStatsItem = {
	change: string;
	changeType: uiStatsChangeType;
	name: string;
	showFooter: boolean;
	stat: string;
	icon: string | FunctionalComponent<HTMLAttributes & VNodeProps, {}>;
	actionText?: string;
	explanation: string;
};

export type uiStatsChangeType = "neutral" | "increase" | "decrease";

export interface getStatsValuesParams {
	brands: Array<OverviewBrandRatio | ComparisonBrandRatio> | null;
	previousBrands: Array<OverviewBrandRatio | ComparisonBrandRatio> | null;
	title: string;
	icon: string | FunctionalComponent<HTMLAttributes & VNodeProps, {}>;
	actionText?: string;
	showFooter?: boolean;
	explanationTranslations: {
		total: string;
		of: string;
	};
}
