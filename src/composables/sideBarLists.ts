import { t } from "@/locales";
import {
	BuildingStorefrontIcon,
	DocumentMagnifyingGlassIcon,
	HomeIcon,
	IdentificationIcon,
} from "@heroicons/vue/24/outline";

export const accountsSideBarList = [
	{
		name: t("sidebar.comparison"),
		icon: HomeIcon,
		current: true,
		id: "comparison",
	},
];

export const brandsSideBarList = [
	{
		name: t("sidebar.overview"),
		icon: HomeIcon,
		current: true,
		id: "overview",
	},
	{
		name: t("sidebar.reservations"),
		icon: DocumentMagnifyingGlassIcon,
		current: false,
		id: "reservations",
	},
	{
		name: t("sidebar.scans"),
		icon: IdentificationIcon,
		current: false,
		id: "scans",
	},
	{
		name: t("sidebar.precheckins"),
		icon: BuildingStorefrontIcon,
		current: false,
		id: "precheckins",
	},
];
