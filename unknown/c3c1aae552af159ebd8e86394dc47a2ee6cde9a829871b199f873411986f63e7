<template>
  <div class="flex flex-col lg:flex-row justify-between items-start">
    <div class="lg:w-2/3 xl:w-1/2">
      <h3 class="text-lg font-medium leading-6 text-gray-900">
        {{ t("brands.text.selectAccountPrompt") }}
      </h3>
      <p class="mt-1 mb-8 text-sm text-gray-500">
        {{ t("brands.text.chainStatsPrompt") }}
      </p>
    </div>
    <uiButton
      class="mb-12 lg:mb-0"
      :loading="false"
      @click="redirectTo('comparison')"
    >
      {{ t("brands.buttons.comparison") }}
    </uiButton>
  </div>
  <div
    class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5"
  >
    <uiCard
      v-for="(brand, index) in getBrandsByAccountId(accountId, searchValue)"
      :id="brand?.brandInfo.name"
      :key="index"
      :loading="false"
      :name="brand?.brandInfo.name"
      :logo="brand?.brandInfo.logo || HOTELINKING_LOGO"
      data-test="brands-cards"
      @click="redirectTo('overview', brand.brandInfo.id)"
    />
    <router-view></router-view>
  </div>
</template>
<script setup lang="ts">
import { HOTELINKING_LOGO } from "@/constants";
import { useBrandStore } from "@/stores/brand";
import { useRoute } from "vue-router";
import { onMounted, inject } from "vue";

import { storeToRefs } from "pinia";
import { t } from "@/locales";
import { useSidebar } from "@/composables/useSidebar";
import { brandsSideBarList } from "@/composables/sideBarLists";

const { sidebarActions } = useSidebar(brandsSideBarList);
const brandStore = useBrandStore();
const { getBrandsByAccountId } = storeToRefs(brandStore);
const route = useRoute();
const accountId = Number(route.params.account_id);
const searchValue = inject("searchValue") as string | null;

onMounted(async (): Promise<void> => {
  const brands = getBrandsByAccountId.value(accountId, null);

  if (brands && !brands.length) {
    redirectTo("accounts");
  }

  if (brands && brands.length === 1) {
    redirectTo("overview", brands[0].brandInfo.id);
  }
});

const redirectTo = (name: string, brandId?: number | undefined): void => {
  sidebarActions(name, brandId?.toString());
};
</script>
