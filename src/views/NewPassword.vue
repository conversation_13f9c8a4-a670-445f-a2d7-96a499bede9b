<template>
  <div
    class="flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8"
  >
    <uiModal
      modal-name="resetPasswordModal"
      :title="t('newPassword.newCode.header')"
      :actions="[
        { value: 'sendNewCode', name: t('newPassword.newCode.resetButton') },
      ]"
      :open="openModal"
      @modal-action="modalActions($event)"
    >
      <p class="mb-4">{{ t("newPassword.newCode.body") }}</p>
      <uiInput
        :value="state.email"
        :loading="loading"
        :label="t('common.email')"
        :color="v$.email.$errors[0]?.$uid ? 'danger' : ''"
        :error="v$.email.$errors[0]?.$uid ? t('auth.emailInputError') : ''"
        name="email"
        type="email"
        required
        @input-changed="state.email = $event.value"
      />
    </uiModal>
    <div class="sm:mx-auto sm:w-full sm:max-w-sm">
      <img
        class="mx-auto w-auto"
        :src="HOTELINKING_AUTOCHECKIN_LOGO"
        alt="Autocheckin logo"
      />
      <h2
        class="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"
      >
        {{ t("newPassword.header") }}
      </h2>
    </div>
    <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
      <form class="space-y-6" action="#" method="POST">
        <uiInput
          :loading="loading"
          :label="t('newPassword.codeLabel')"
          :value="state.code"
          :color="v$.code.$errors[0]?.$uid ? 'danger' : ''"
          :error="
            v$.code.$errors[0]?.$uid ? t('newPassword.codeInputEmpty') : ''
          "
          name="code"
          @input-changed="state.code = $event.value"
        />
        <uiInput
          :loading="loading"
          :label="t('common.password')"
          :value="state.password"
          :color="v$.password.$errors[0]?.$uid ? 'danger' : ''"
          :error="
            v$.password.$errors[0]?.$uid ? t('auth.passwordInputError') : ''
          "
          name="password"
          type="password"
          @input-changed="state.password = $event.value"
        />
        <uiInput
          :loading="loading"
          :label="t('newPassword.confirmLabel')"
          :value="state.confirmationPassword"
          :error="
            v$.confirmationPassword.$errors[0]?.$uid
              ? t('newPassword.passwordMatch')
              : ''
          "
          :color="v$.confirmationPassword.$errors[0]?.$uid ? 'danger' : ''"
          name="password"
          type="password"
          @input-changed="state.confirmationPassword = $event.value"
        />
        <div class="flex flex-col sm:flex-row justify-between items-center">
          <uiButton :loading="loading" @click="forgotPasswordSubmit">{{
            t("newPassword.changePasswordButton")
          }}</uiButton>
          <p class="mt-4 sm:mt-0 text-sm text-gray-500">
            <span
              class="text-indigo-600 hover:text-indigo-900 cursor-pointer font-bold"
              @click="openModal = true"
              >{{ t("newPassword.newCode.header") }}</span
            >
          </p>
        </div>
        <div class="relative">
          <div class="absolute inset-0 flex items-center" aria-hidden="true">
            <div class="w-full border-t border-gray-300" />
          </div>
          <div class="relative flex justify-center">
            <span class="px-2 text-sm text-gray-500 bg-gray-50">{{
              t("newPassword.alternativeText")
            }}</span>
          </div>
        </div>
        <p
          class="text-sm text-indigo-600 hover:text-indigo-900 cursor-pointer text-center"
          @click="returnToLogin"
        >
          {{ t("newPassword.returnToLogin") }}
        </p>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { HOTELINKING_AUTOCHECKIN_LOGO } from "@/constants";
import { ref } from "vue";
import { useUserStore } from "@/stores/user";
import { reactive, computed } from "vue";
import { required, requiredIf } from "@vuelidate/validators";
import { useVuelidate } from "@vuelidate/core";
import { useRouter } from "vue-router";
import { t } from "@/locales";
import { goToBrands } from "@/helpers/router";
import { useNotificationStore } from "@/stores/notification";
import { type uiModalEvent } from "@/uiTypes";
import { sameAs } from "@vuelidate/validators";

const loading = ref(false);
const openModal = ref(false);

const userStore = useUserStore();
const notificationStore = useNotificationStore();

const router = useRouter();

const state = reactive({
  // if the user clicks on new code, they will be prompted to enter the email again. This value will depend on that input.
  email: "",
  code: "",
  password: "",
  confirmationPassword: "",
});

//Validation rules
const rules = computed(() => {
  return {
    email: { requiredIfNoCode: requiredIf(!state.code) },
    code: { required },
    password: { required },
    confirmationPassword: { sameAs: sameAs(state.password) },
  };
});

const v$ = useVuelidate(rules, state);

const modalActions = (event: uiModalEvent): void => {
  if (event.action === "close") openModal.value = false;
  if (event.action === "sendNewCode") {
    sendNewCode();
  }
};

function returnToLogin(): void {
  router.push({ name: "login" });
}

async function sendNewCode(): Promise<void> {
  const emailValid = await v$.value.email.$validate();
  if (!emailValid) return;
  // We replace the email from the modal with the new email that was introduced
  userStore.setEmail(state.email);
  await userStore.forgotPassword(state.email);
  if (userStore.error) {
    switch (userStore.error) {
      case "UserNotFoundException":
        notificationStore.error(
          t("auth.userNotFoundError.title"),
          t("auth.userNotFoundError.body")
        );
        break;
      default:
        notificationStore.error(
          t("auth.defaultError.title"),
          t("auth.defaultError.body")
        );
    }
  } else {
    openModal.value = false;
  }
}

async function forgotPasswordSubmit(): Promise<void> {
  const valid = await v$.value.$validate();
  if (!valid) return;
  await userStore.forgotPasswordSubmit({
    email: userStore.email,
    password: state.password,
    code: state.code,
  });
  if (userStore.error) {
    switch (userStore.error) {
      case "CodeMismatchException":
        notificationStore.error(
          t("newPassword.codeError.title"),
          t("newPassword.codeError.body")
        );
        break;
      case "LimitExceededException":
        notificationStore.error(
          t("newPassword.attemptsError.title"),
          t("newPassword.attemptsError.body")
        );
        break;
      case "ExpiredCodeException":
        notificationStore.error(
          t("newPassword.expiredError.title"),
          t("newPassword.expiredError.body")
        );
        break;
      default:
        notificationStore.error(
          t("auth.defaultError.title"),
          t("auth.defaultError.body")
        );
    }
  } else {
    await userStore.login({
      email: userStore.email,
      password: state.password,
    });
    goToBrands();
  }
}
</script>
