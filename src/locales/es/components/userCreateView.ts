export default {
	title: "Crear nuevo usuario",
	usersCreateTitle: "Rellene el siguiente formulario para crear un usuario",
	usersCreateText:
		"Introduzca el email del usuario, los IDs de los establecimientos que va a gestionar y su nivel de gestión dentro de los establecimientos.",
	emailPlaceholder: "Escriba un correo electrónico...",
	emailLabel: "Correo electrónico del usuario",
	brandsLabel: "IDs de establecimientos",
	brandsPlaceholder: "Separados por comas (1,2,3..)",
	roles: {
		admin: "admin",
		brandAdmin: "brand_admin",
		reception: "reception",
	},
	roleLabel: "Nivel de gestión",
	successUserCreateTitle: "Usuario creado con éxito",
	errorUserCreateTitle: "Error al intentar crear el usuario",
	errorUserCreateMessage:
		"Ha ocurrido un error al intentar crear el usuario. Vuelva a intentarlo más tarde o póngase en contacto con nuestro equipo de Customer Success.",
	userData: "Aquí tiene los datos del nuevo usuario que ha creado:",
	userEmail: "Email de usuario:",
	storedPassword: "Contraseña generada:",
	brandIds: "Ids de marcas:",
	role: "Rol asignado:",
	breadCrumb: {
		selector: "Crear nuevo usuario",
	},
	success: {
		title: "Nuevo usuario creado",
		body: "El usuario ha sido creado correctamente.",
	},
	brandsError: "Debe introducir al menos un ID de establecimiento",
	groupsError: "Debe seleccionar al menos un grupo",
	mfaLabel: "Habilitar MFA",
};
