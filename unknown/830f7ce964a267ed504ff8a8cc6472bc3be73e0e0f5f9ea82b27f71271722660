export default {
	header: "Identifícate para acceder",
	logInButton: "Acceder",
	forgotPassword: "Olvidaste tu contraseña?",
	resetPasswordButton: "resetéala",
	sendCodeButton: "Mandar código",
	requireVerification: "Verificación adicional necesaria",
	identificationError: {
		title: "Error al identificar",
		body: "Ha introducido credenciales incorrectas, por favor, inténtelo de nuevo o póngase contacto con nuestro equipo de Customer Success.",
	},
	defaultError: {
		title: "Ocurrió un error desconocido.",
		body: "Por favor, póngase en contacto con soporte.",
	},
	userNotFoundError: {
		title: "Usuario no encontrado.",
		body: "El usuario no existe, por favor intente con otro.",
	},
	resetPassword: {
		header: "Resetea tu contraseña",
		body: "Escribe tu correo electrónico. Si hay una contraseña asociada con este te mandaremos un email con instrucciones para resetearla.",
		resetButton: "Resetear mi contraseña",
	},
	newPassword: {
		header: "Se requiere una nueva contraseña",
		body: "Ingresa tu nueva contraseña",
		resetButton: "Cambiar mi contraseña",
	},
	emailInputError: "El campo tiene que contener un correo electrónico válido",
	passwordInputError: "El campo no puede estar vacio",
	isAuth: {
		title: "Autorización correcta",
		body: "El usuario ha sido autorizado correctamente.",
	},
};
