<template>
  <uiTopbar
    :logo="HOTELINKING_AUTOCHECKIN_LOGO"
    :account-logo="accountLogo"
    :profile-menu="uiTopBarOptions"
    :navigation="sidebar"
    @top-bar-click="sidebarActions"
  />
</template>

<script setup lang="ts">
import { t } from "@/locales";
import { computed } from "vue";
import { useSidebar } from "@/composables/useSidebar";
import { useBrandStore } from "@/stores/brand";
import { useRoute } from "vue-router";
import { storeToRefs } from "pinia";
import { brandsSideBarList } from "@/composables/sideBarLists";
import { HOTELINKING_AUTOCHECKIN_LOGO, HOTELINKING_LOGO } from "@/constants";
const route = useRoute();
import { useUserStore } from "@/stores/user";
const userStore = useUserStore();

const brandStore = useBrandStore();
const { getBrandById } = storeToRefs(brandStore);
const uiTopBarOptions = computed(() => {
  const isAdmin = userStore.isAdmin;
  // Sidebar options
  const userManagementOption = {
    name: t("common.userManagement"),
    id: "userManagement",
  };
  const logoutOption = { name: t("common.logout"), id: "logout" };
  return isAdmin ? [userManagementOption, logoutOption] : [logoutOption];
});
const { sidebar, sidebarActions } = useSidebar(brandsSideBarList);
const accountLogo = computed(() => {
  const brandId = Number(route.params.brand_id);
  const brand = getBrandById.value(brandId);
  return brand?.logo ? brand.logo : HOTELINKING_LOGO;
});
</script>
