<template>
  <div class="filters grow">
    <uiInput
      :loading="false"
      class="w-full"
      :placeholder="placeholder"
      :value="searchValue"
      @input-changed="inputChanged"
    />
  </div>
</template>

<script setup lang="ts">
import { useRouteQuery } from "@vueuse/router";
import { t } from "@/locales";
import type { uiInputEventChange } from "@/uiTypes";

const searchValue = useRouteQuery("search_value", "", { transform: String });
const placeholder = t("brands.filter.name");
const inputChanged = (event: uiInputEventChange): void => {
  searchValue.value = event.value;
};
</script>
