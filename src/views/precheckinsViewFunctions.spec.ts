import { describe, it, expect, expectTypeOf, vi } from "vitest";
import { beforeEach } from "vitest";
import { assertFormattedDataIsNotNull } from "@/helpers/testHelpers";
import { useCheckinStore } from "@/stores/checkin";
import { useBrandStore } from "@/stores/brand";

import { createPinia, setActivePinia } from "pinia";
import flushPromises from "flush-promises";
import { mount } from "@vue/test-utils";
import PrecheckinsViewVue from "@/views/PrecheckinsView.vue";
import { ref } from "vue";
import messages from "@/locales/en";
import mocks from "@/../mocks/modules/Request/requestMock.json";
import { t } from "@/locales";

describe("formats data correctly", async () => {
	beforeEach(() => {
		vi.mock("@vueuse/router", () => ({
			useRouteQuery: vi.fn(() => ref()),
		}));

		vi.mock("vue-router", async () => {
			const actual = (await vi.importActual("vue-router")) as object;
			return {
				...actual,
				useRoute: vi.fn().mockReturnValue({
					params: { brand_id: "1" },
				}),
				useRouter: vi.fn(() => ref()),
			};
		});

		setActivePinia(createPinia());
		const checkinStore = useCheckinStore();
		const brandStore = useBrandStore();

		checkinStore.getCheckins = vi.fn();
		brandStore.$state.brands = [
			{
				brandInfo: {
					id: 1,
					time_zone: "Europe/Madrid",
				},
			},
		];
	});

	it("returns an array of formatted data and adapts header", async () => {
		const checkinStore = useCheckinStore();
		checkinStore.$state.items = null;

		const wrapper = await mount(PrecheckinsViewVue);
		checkinStore.$state.items = mocks.precheckin.mockData1;

		await flushPromises();
		const { formattedCheckins, header } = wrapper.vm;
		assertFormattedDataIsNotNull(formattedCheckins);
		expect(formattedCheckins).toStrictEqual(mocks.precheckin.expectedResponse1);
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.errorCode"), value: "error_code" },
			{ name: t("filterTable.source.title"), value: "source" },
			{ name: t("filterTable.reservationId"), value: "" },
			{ name: t("filterTable.checkIn"), value: "" },
			{ name: t("filterTable.checkOut"), value: "" },
			{ name: t("filterTable.firstName"), value: "" },
			{ name: t("filterTable.lastName"), value: "" },
			{ name: t("filterTable.nationality"), value: "" },
		]);
		formattedCheckins.forEach((checkin) => {
			expectTypeOf(checkin).toBeObject();
			expect(checkin).toHaveProperty("id");
			expect(checkin).toHaveProperty("row");

			expectTypeOf(checkin.id).toBeNumber();
			expectTypeOf(checkin.row).toBeArray();
		});
	});

	it("formats data without errors correctly and adapts header", async () => {
		const mockDataSuccess = [
			{
				time: "2023-11-16T15:30:00.000Z",
				trace_id: "random-trace-id",
				name: "random-name",
				error: "0",
				source: "reception",
				params: {
					res_id: "random-res-id",
					check_in: "2023/02/01",
					check_out: "2023/02/10",
					guests: [
						{
							name: "Al**",
							surname: null,
							nationality: "ESP",
						},
					],
				},
				response: "",
			},
		];
		const checkinStore = useCheckinStore();
		checkinStore.$state.items = null;

		const wrapper = await mount(PrecheckinsViewVue);
		checkinStore.$state.items = mockDataSuccess;

		await flushPromises();

		const { formattedCheckins, header } = wrapper.vm;
		expect(formattedCheckins[0].row[0]).toBe(mockDataSuccess[0].trace_id);
		expect(formattedCheckins[0].row[1]).toBe("2023-11-16 16:30:00");
		expect(formattedCheckins[0].row[2].content).toBe(messages.common.success);
		expect(formattedCheckins[0].row[3]).toBe(
			messages.filterTable.source.types.reception,
		);
		expect(formattedCheckins[0].row[4]).toBe(mockDataSuccess[0].params.res_id);
		expect(formattedCheckins[0].row[5]).toBe(
			mockDataSuccess[0].params.check_in,
		);
		expect(formattedCheckins[0].row[6]).toBe(
			mockDataSuccess[0].params.check_out,
		);
		expect(formattedCheckins[0].row[7]).toBe(
			mockDataSuccess[0].params.guests[0].name,
		);
		expect(formattedCheckins[0].row[8]).toBe(
			mockDataSuccess[0].params.guests[0].nationality,
		);
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.source.title"), value: "source" },
			{ name: t("filterTable.reservationId"), value: "" },
			{ name: t("filterTable.checkIn"), value: "" },
			{ name: t("filterTable.checkOut"), value: "" },
			{ name: t("filterTable.firstName"), value: "" },
			{ name: t("filterTable.nationality"), value: "" },
		]);
	});

	it("formats data with errors correctly, show correct error Messages and adapts header", async () => {
		const checkinStore = useCheckinStore();
		checkinStore.$state.items = null;

		const newMock = [mocks.precheckin.mockData1[1]];

		const wrapper = await mount(PrecheckinsViewVue);
		checkinStore.$state.items = newMock;

		await flushPromises();

		const { formattedCheckins, header } = wrapper.vm;
		const expectedErrorContent = {
			content: "Error",
			color: "danger",
			type: "tag",
		};
		const expectedErrorMessage = "Reservation Is In Precheckin State";
		expect(formattedCheckins[0].row[2]).toEqual(expectedErrorContent);
		expect(formattedCheckins[0].row[3]).toBe(expectedErrorMessage);
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.errorCode"), value: "error_code" },
			{ name: t("filterTable.source.title"), value: "source" },
			{ name: t("filterTable.reservationId"), value: "" },
			{ name: t("filterTable.checkIn"), value: "" },
			{ name: t("filterTable.checkOut"), value: "" },
			{ name: t("filterTable.firstName"), value: "" },
			{ name: t("filterTable.lastName"), value: "" },
			{ name: t("filterTable.nationality"), value: "" },
		]);
	});
	it("formats success data with missing optional fields correctly and adapts header", async () => {
		const mockDataSuccessMissingFields = [
			{
				time: "2023-11-16T15:30:00.000Z",
				trace_id: "aa488687-d0c4-4458-854c-daca818e2e02",
				name: "checkin",
				error: "0",
				source: "autocheckin",
				params: {
					res_id: null,
					check_in: null,
					check_out: null,
					guests: [
						{
							name: null,
							surname: null,
							second_surname: null,
							nationality: null,
						},
					],
				},
				response: "",
			},
		];
		const checkinStore = useCheckinStore();
		checkinStore.$state.items = null;

		const wrapper = await mount(PrecheckinsViewVue);
		checkinStore.$state.items = mockDataSuccessMissingFields;

		await flushPromises();

		const { formattedCheckins, header } = wrapper.vm;
		expect(formattedCheckins[0].row).toEqual([
			"aa488687-d0c4-4458-854c-daca818e2e02",
			"2023-11-16 16:30:00",
			{ color: "success", content: "Success", type: "tag" },
			"Autocheckin",
		]);
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.source.title"), value: "source" },
		]);
	});
});
