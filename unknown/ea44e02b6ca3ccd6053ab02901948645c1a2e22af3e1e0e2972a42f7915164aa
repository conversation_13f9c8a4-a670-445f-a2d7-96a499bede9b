{"mockData1": [{"trace_id": "tAowk949I1de0K9f", "time": "2023-01-26T15:30:00.000Z", "error": "0", "error_code": null, "source": "autocheckin", "params": {"manual": true, "reservation_code": "FYTNL"}}, {"trace_id": "roQDE2a0zTtawIZf", "time": "2013-05-29T10:00:00.000Z", "error": "1", "error_code": "INT_2_8", "source": "autocheckin", "params": {"manual": true, "check_in": "2022/10/12", "first_name": "<PERSON>"}, "response": {"error": {"code": "INT_2_8", "message": "Any message"}}}, {"trace_id": "JLZ5mZNmGyOSEMfe", "time": "2010-01-23T10:00:00.000Z", "error": "1", "error_code": "INT_1_2", "source": "autocheckin", "params": {"manual": true, "check_out": "2023/09/08", "email": "<PERSON>_<PERSON><PERSON><EMAIL>", "first_name": "<PERSON><PERSON><PERSON>"}, "response": {"error": {"code": "INT_1_2"}}}, {"trace_id": "7zMjonVVAujHcr4s", "time": "2009-04-22T10:00:00.000Z", "error": "0", "error_code": null, "source": "autocheckin", "params": {"manual": true, "check_in": "2022/12/05", "check_out": "2024/04/03", "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "reservation_code": "BJPiT"}}, {"trace_id": "COqCo0gIfY7143QK", "time": "2011-02-27T10:00:00.000Z", "error": "0", "error_code": null, "source": "reception", "params": {"manual": true, "last_name": "<PERSON><PERSON>"}}], "expectedResponse1": [{"id": 1, "row": ["tAowk949I1de0K9f", "2023-01-26 16:30:00", {"color": "success", "content": "Success", "type": "tag"}, "-", "FYTNL", "Autocheckin", "-", "-", "-", "-", "-", "Yes"]}, {"id": 2, "row": ["roQDE2a0zTtawIZf", "2013-05-29 12:00:00", {"color": "danger", "content": "Error", "type": "tag"}, {"content": "Response Not Valid", "emits": "showMessageModal", "message": "Any message", "type": "link"}, "-", "Autocheckin", "<PERSON>", "-", "2022/10/12", "-", "-", "Yes"]}, {"id": 3, "row": ["JLZ5mZNmGyOSEMfe", "2010-01-23 11:00:00", {"color": "danger", "content": "Error", "type": "tag"}, "Bad Params To Mount Request", "-", "Autocheckin", "<PERSON><PERSON><PERSON>", "-", "-", "2023/09/08", "<PERSON>_<PERSON><PERSON><EMAIL>", "Yes"]}, {"id": 4, "row": ["7zMjonVVAujHcr4s", "2009-04-22 12:00:00", {"color": "success", "content": "Success", "type": "tag"}, "-", "BJPiT", "Autocheckin", "<PERSON>", "<PERSON><PERSON>", "2022/12/05", "2024/04/03", "<EMAIL>", "Yes"]}, {"id": 5, "row": ["COqCo0gIfY7143QK", "2011-02-27 11:00:00", {"color": "success", "content": "Success", "type": "tag"}, "-", "-", "Reception", "-", "<PERSON><PERSON>", "-", "-", "-", "Yes"]}]}