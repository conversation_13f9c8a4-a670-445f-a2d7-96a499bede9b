import { API } from "@aws-amplify/api";
import type { ApiRequestResponse, SearchFilters } from "@/types";

export default {
	async get(
		brandId: number,
		queryParams: SearchFilters,
	): Promise<ApiRequestResponse> {
		try {
			return API.get("stats", `autocheckin/brands/${brandId}/stats/request`, {
				queryStringParameters: queryParams,
			});
		} catch (error) {
			console.error(error);
			throw new Error("Failed to fetch reservations");
		}
	},
};
