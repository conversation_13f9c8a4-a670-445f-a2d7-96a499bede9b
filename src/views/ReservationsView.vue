<template>
  <TableWrapper
    :header="header"
    :ordered_by="table.orderedBy"
    :order_direction="table.orderDirection"
    :items="formattedReservations"
    :filters="table.filters"
    :last_page="last_page"
    @filters-updated="getReservations"
    @home="handleNoResults('home')"
  >
  </TableWrapper>
</template>

<script setup lang="ts">
import TableWrapper from "@/components/TableWrapper.vue";
import { storeToRefs } from "pinia";
import { computed, watch, ref} from "vue";
import { useReservationStore } from "@/stores/reservation";
import { useBrandStore } from "@/stores/brand";
import { useRoute } from "vue-router";
import type {
  SearchFilters,
  ErrorObject,
  ErrorMessageObject,
  FormattedData,
  Column
} from "@/types";
import { t } from "@/locales";
import { formatDataExtended, removeEmptyReservationsValues, filterEmptyFields } from "@/helpers/uiTable";
import router from "@/router";

const route = useRoute();

const brandId = ref(Number(route.params.brand_id));
const brandStore = useBrandStore();
const { getBrandById } = storeToRefs(brandStore);

const reservationStore = useReservationStore();
const { last_page } = storeToRefs(reservationStore); // update local ref when table wrapper emits the table-updated

const formattedReservations = ref<FormattedData[] | null>(null);
const columnIndicesToRemove = ref<number[]>([]);

const getReservations = async (filters: SearchFilters) => {
  await reservationStore.getReservations(brandId.value, filters);
};

const brandTimeZone = computed(() => {
   return getBrandById.value(brandId.value)?.time_zone; 
});

watch(
  () => reservationStore.items, (newItems) => {
    if (!newItems) {
      formattedReservations.value = null  
      columnIndicesToRemove.value = []
      return 
    }
 
    const allRows = newItems.map((reservation, i) => {
      const { trace_id, time, error, params, response, source } = reservation;
      const { formattedTime, errorTag, errorMessage, sourceTranslation } = formatDataExtended(time, error, source, response, brandTimeZone.value);

      const newRow: (string | ErrorObject | ErrorMessageObject)[] = [
        trace_id,
        formattedTime,
        errorTag,
        errorMessage,
        params.reservation_code || "-",
        sourceTranslation,
        params.room_number || "-",
        params.first_name || "-",
        params.last_name || "-",
        params.check_in || "-",
        params.check_out || "-",
        params.email || "-",
        params.manual !== undefined ? (params.manual ? "Yes" : "No") : "-",
      ];

      return {
        id: i + 1,
        row: newRow,
      };
    });
     
    const { filteredRows, columnIndicesToRemove: indicesToRemove } = removeEmptyReservationsValues(allRows);
 
    formattedReservations.value = filteredRows;
    columnIndicesToRemove.value = indicesToRemove;
  } 
);

const handleNoResults = (action: string) => {
  if (action === "home") {
    router.push({ name: "overview" });
  }
};

const header = computed((): Column[]  => {
  const columns = [
    { name: t("filterTable.sessionId"), value: "trace_id" },
    { name: t("filterTable.date"), value: "time" },
    { name: t("filterTable.result"), value: "error" },
    { name: t("filterTable.errorCode"), value: "error_code" },
    { name: t("filterTable.reservationCode"), value: "" },
    { name: t("filterTable.source.title"), value: "source" },
    { name: t("filterTable.roomNumber"), value: "" },
    { name: t("filterTable.firstName"), value: "" },
    { name: t("filterTable.lastName"), value: "" },
    { name: t("filterTable.checkIn"), value: "" },
    { name: t("filterTable.checkOut"), value: "" },
    { name: t("common.email"), value: "" },
    { name: t("filterTable.manual"), value: "" },
  ];

  return filterEmptyFields<Column>(columns, columnIndicesToRemove.value)
});

const table = {
  orderedBy: "time",
  orderDirection: "desc",
  filters: [
    { name: t("filterTable.sessionId"), value: "trace_id" },
    { name: t("filterTable.result"), value: "error" },
    { name: t("filterTable.errorCode"), value: "error_code" },
    { name: t("filterTable.reservationCode"), value: "reservation_code" },
    { name: t("filterTable.source.title"), value: "source" },
    { name: t("filterTable.roomNumber"), value: "room_number" },
    { name: t("filterTable.firstName"), value: "first_name" },
    { name: t("filterTable.lastName"), value: "last_name" },
    { name: t("filterTable.checkIn"), value: "check_in" },
    { name: t("filterTable.checkOut"), value: "check_out" },
    { name: t("common.email"), value: "email" },
    { name: t("filterTable.manual"), value: "manual" },
  ],
};
// to access in wrapper.vn, we need to expose it
defineExpose({ formattedReservations });
</script>