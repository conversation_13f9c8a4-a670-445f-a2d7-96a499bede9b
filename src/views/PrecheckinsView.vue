<template>
  <TableWrapper
    :header="header"
    :ordered_by="table.orderedBy"
    :order_direction="table.orderDirection"
    :items="formattedCheckins"
    :filters="table.filters"
    :last_page="last_page"
    @filters-updated="getCheckins"
    @home="handleNoResults('home')"
  >
  </TableWrapper>
</template>

<script setup lang="ts">
import TableWrapper from "@/components/TableWrapper.vue";
import { storeToRefs } from "pinia";
import { t } from "@/locales";
import { computed, watch, ref} from "vue";
import { formatDataExtended, removeEmptyReservationsValues, filterEmptyFields  } from "@/helpers/uiTable";
import { useCheckinStore } from "@/stores/checkin";
import { useBrandStore } from "@/stores/brand";
import { useRoute } from "vue-router";
import type {
  SearchFilters,
  ErrorObject,
  ErrorMessageObject,
  FormattedData,
  Column
} from "@/types";
import router from "@/router";

const route = useRoute();

const brandId = ref(Number(route.params.brand_id));
const brandStore = useBrandStore();
const { getBrandById } = storeToRefs(brandStore);

const checkinStore = useCheckinStore();
const { last_page } = storeToRefs(checkinStore); // update local ref when table wrapper emits the table-updated

const formattedCheckins = ref<FormattedData[] | null>(null);
const columnIndicesToRemove = ref<number[]>([]);

const getCheckins = async (filters: SearchFilters) => {
  checkinStore.getCheckins(brandId.value, filters);
};

const brandTimeZone = computed(() => {
   return getBrandById.value(brandId.value)?.time_zone; 
});

watch(
  () => checkinStore.items, (newItems) => {
    if (!newItems) {
      formattedCheckins.value = null  
      columnIndicesToRemove.value = []
      return 
    }
 
    const allRows = newItems.map((checkin, i) => {
      const { trace_id, time, error, params, response, source } = checkin;
      const { formattedTime, errorTag, errorMessage, sourceTranslation } = formatDataExtended(time, error, source, response, brandTimeZone.value);

      const newRow: (string | ErrorObject | ErrorMessageObject)[] = [
        trace_id,
        formattedTime,
        errorTag,
        errorMessage,
        sourceTranslation,
        params.res_id || "-",
        params.check_in || "-",
        params.check_out || "-",
        params.guests[0].name || "-",
        params.guests[0].surname || "-",
        params.guests[0].nationality || "-",
      ];

      return {
        id: i + 1,
        row: newRow,
      };
    });
     
    const { filteredRows, columnIndicesToRemove: indicesToRemove } = removeEmptyReservationsValues(allRows);
 
    formattedCheckins.value = filteredRows;
    columnIndicesToRemove.value = indicesToRemove;
  } 
);


const handleNoResults = (action: string) => {
  if (action === "home") {
    router.push({ name: "overview" });
  }
};

const header = computed((): Column[]  => {
  const columns = [
    { name: t("filterTable.sessionId"), value: "trace_id" },
    { name: t("filterTable.date"), value: "time" },
    { name: t("filterTable.result"), value: "error" },
    { name: t("filterTable.errorCode"), value: "error_code" },
    { name: t("filterTable.source.title"), value: "source" },
    { name: t("filterTable.reservationId"), value: "" },
    { name: t("filterTable.checkIn"), value: "" },
    { name: t("filterTable.checkOut"), value: "" },
    { name: t("filterTable.firstName"), value: "" },
    { name: t("filterTable.lastName"), value: "" },
    { name: t("filterTable.nationality"), value: "" },
  ];

  return filterEmptyFields<Column>(columns, columnIndicesToRemove.value)
});


const table = {
  orderedBy: "time",
  orderDirection: "desc",
  filters: [
    { name: t("filterTable.sessionId"), value: "trace_id" },
    { name: t("filterTable.result"), value: "error" },
    { name: t("filterTable.errorCode"), value: "error_code" },
    { name: t("filterTable.source.title"), value: "source" },
    { name: t("filterTable.reservationId"), value: "res_id" },
    { name: t("filterTable.checkIn"), value: "check_in" },
    { name: t("filterTable.checkOut"), value: "check_out" },
    { name: t("filterTable.firstName"), value: "name" },
    { name: t("filterTable.lastName"), value: "surname" },
    { name: t("filterTable.nationality"), value: "nationality" },
  ],
};
</script>
