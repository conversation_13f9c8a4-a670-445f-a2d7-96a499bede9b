import { describe, it, expect } from "vitest";
import { sanitizeString } from "./strings";

describe("Testing sanitizeString method", () => {
	it("Accents should be removed", () => {
		expect(sanitizeString("Mónaco")).toEqual("Monaco");
	});
	it("umlaut shoud be removed", () => {
		expect(sanitizeString("Müller")).toEqual("<PERSON>");
	});
	it("Circumflex shoud be removed", () => {
		expect(sanitizeString("île")).toEqual("ile");
	});
	it("diacritic shoud be removed", () => {
		expect(sanitizeString("Mak<PERSON>lélé")).toEqual("Makelele");
	});
	it("ç shoud be removed", () => {
		expect(sanitizeString("Calçot")).toEqual("Calcot");
	});
});
