import { useSidebar } from "@/composables/useSidebar";
import { ref } from "vue";
import { createRouter, createWebHistory } from "vue-router";
import { config, mount } from "@vue/test-utils";
import { createPinia } from "pinia";
import { describe, it, expect, vi } from "vitest";
import { useUserStore } from "@/store/user";

vi.mock("@/store/user", () => ({
	useUserStore: vi.fn(() => {
		return { logout: vi.fn() };
	}),
}));

// vi.mock("vue-router", () => ({
// 	useRouter: vi.fn().mockReturnValue({
// 		push: vi.fn(),
// 	}),
// 	useRoute: vi.fn().mockReturnValue(ref("overview")),
// }));

describe("useSidebar", () => {
	// const pinia = createPinia();
	const mockRouter = createRouter({
		history: createWebHistory(),
		routes: [
			{
				name: "test",
				path: "/test",
			},
			{
				name: "login",
				path: "/login",
			},
		],
	});

	// config.global.plugins = [pinia];
	config.global.mocks.$router = mockRouter;

	// const wrapper = mount({}, { global: config.global });

	it("should initialize with default values", () => {
		// const { sidebar } = useSidebar();
		// expect(sidebar.value).toHaveLength(5);
		// expect(sidebar.value[0].current).toBe(true);
		// expect(sidebar.value[1].current).toBe(false);
		expect(true).toBe(true);
	});

	// it("should change the current route when sidebarActions is called", async () => {
	// 	const { sidebarActions, sidebar } = useSidebar();
	// 	await sidebarActions("reservations");

	// 	expect(sidebar.value[0].current).toBe(false);
	// 	expect(sidebar.value[1].current).toBe(true);
	// 	expect(mockRouter.push).toHaveBeenCalledWith({ name: "reservations" });
	// });

	// it("should call logout and refresh the page when logout is selected", async () => {
	// 	window.location = undefined;
	// 	window.location = { reload: vi.fn() };

	// 	const { sidebarActions } = useSidebar();
	// 	await sidebarActions("logout");

	// 	expect(pinia.useUserStore().logout).toHaveBeenCalled();
	// 	expect(window.location.reload).toHaveBeenCalled();
	// });
});
