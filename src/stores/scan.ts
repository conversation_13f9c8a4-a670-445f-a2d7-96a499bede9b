import { defineStore } from "pinia";
import RequestRepository from "@/modules/Request/request";
import type { SearchFilters, ScanData } from "@/types";

interface State {
	items: ScanData[] | null;
	last_page: number | null;
}

export const useScanStore = defineStore("scan", {
	state: (): State => {
		return {
			items: null,
			last_page: null,
		};
	},
	actions: {
		async getScans(brandId: number, params: SearchFilters) {
			try {
				this.items = null;
				this.last_page = null;

				const response = await RequestRepository.get(brandId, {
					name: "scan",
					...params,
				});

				this.items = (response?.data as ScanData[]) ?? [];
				this.last_page = response.meta.last_page;
				return Promise.resolve();
			} catch (error) {
				console.error("Failed to fetch checkins", error);
				return Promise.reject();
			}
		},
	},
});
