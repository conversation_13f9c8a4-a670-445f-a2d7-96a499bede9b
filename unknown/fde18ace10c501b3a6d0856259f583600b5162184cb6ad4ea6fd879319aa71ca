import { defineStore } from "pinia";
import RequestRepository from "@/modules/Request/request";
import type { SearchFilters, CheckinData } from "@/types";

interface State {
	items: CheckinData[] | null;
	last_page: number | null;
}

export const useCheckinStore = defineStore("checkin", {
	state: (): State => {
		return {
			items: null,
			last_page: null,
		};
	},
	actions: {
		async getCheckins(brandId: number, params: SearchFilters) {
			try {
				this.items = null;
				this.last_page = null;

				const response = await RequestRepository.get(brandId, {
					name: "checkin",
					...params,
				});

				this.items = (response?.data as CheckinData[]) ?? [];
				this.last_page = response.meta.last_page;
				return Promise.resolve();
			} catch (error) {
				console.error("Failed to fetch checkins", error);
				return Promise.reject();
			}
		},
	},
});
