import { test as setup, expect } from "@playwright/test";

const authFile = "playwright/.auth/user.json";

setup("authenticate", async ({ page }) => {
	// Perform authentication steps. Replace these actions with your own.
	await page.goto("/dashboard/login");
	await page.locator('[name="email"]').fill("<EMAIL>");
	await page.locator('[name="password"]').fill("Temporal123");
	await page.locator('[data-test="logInButton"]').click();
	// Wait until the page receives the cookies.
	//
	// Sometimes login flow sets cookies in the process of several redirects.
	// Wait for the final URL to ensure that the cookies are actually set.
	// await page.waitForURL("/");
	const title = await page.waitForSelector('[data-test="page-title"]');
	expect(await title.isVisible()).toBe(true);
	await expect(page).toHaveURL("/dashboard/accounts");
	// Alternatively, you can wait until the page reaches a state where all cookies are set.
	// await expect(page.getByRole('button', { name: 'View profile and more' })).toBeVisible();

	// End of authentication steps.

	await page.context().storageState({ path: authFile });
});
