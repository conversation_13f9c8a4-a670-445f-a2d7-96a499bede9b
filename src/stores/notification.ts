import { defineStore } from "pinia";

interface State {
	message: string;
	title: string;
	show: boolean;
	type?: string;
}

const DEFAULT_TIME_SHOWN = 10000;

export const useNotificationStore = defineStore("notification", {
	state: (): State => {
		return {
			message: "message",
			title: "title",
			show: false,
		};
	},
	actions: {
		success(title: string, message: string) {
			this.type = "success";
			this.title = title;
			this.message = message;
			this.showNotification();
		},
		error(title: string, message: string) {
			this.type = "danger";
			this.title = title;
			this.message = message;
			this.showNotification();
		},
		showNotification() {
			this.show = true;
			setTimeout(() => {
				this.show = false;
			}, DEFAULT_TIME_SHOWN);
		},
		clear() {
			this.message = "";
			this.title = "";
			this.show = false;
		},
	},
});
