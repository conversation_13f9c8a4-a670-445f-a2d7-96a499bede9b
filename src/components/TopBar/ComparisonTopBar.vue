<template>
  <uiTopbar
    :logo="HOTELINKING_AUTOCHECKIN_LOGO"
    :account-logo="accountLogo"
    :profile-menu="uiTopBarOptions"
    :navigation="sidebar"
    @top-bar-click="sidebarActions"
  />
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useSidebar } from "@/composables/useSidebar";
import { useBrandStore } from "@/stores/brand";
import { t } from "@/locales";
import { useRoute } from "vue-router";
import { storeToRefs } from "pinia";
import { accountsSideBarList } from "@/composables/sideBarLists";
import { HOTELINKING_AUTOCHECKIN_LOGO, HOTELINKING_LOGO } from "@/constants";

const route = useRoute();
const brandStore = useBrandStore();
const { getBrandById } = storeToRefs(brandStore);
const uiTopBarOptions = [{ name: t("common.logout"), id: "logout" }];

const { sidebar, sidebarActions } = useSidebar(accountsSideBarList);
const accountLogo = computed(() => {
  const brandId = Number(route.params.brand_id);
  const brand = getBrandById.value(brandId);
  return brand?.logo ? brand.logo : HOTELINKING_LOGO;
});
</script>
