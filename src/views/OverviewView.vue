<script setup lang="ts">
import { t } from "@/locales";
import { computed, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import { useOverviewStore } from "@/stores/overview";
import { useBrandStore } from "@/stores/brand";
import { makeSeries, makeOptions, redirectToTable } from "@/helpers/uiChart";
import { getFunnelPages, getFunnelSeries } from "@/helpers/uiFunnelChart";
import { getStatValues } from "@/helpers/uiStats";
import {
  DocumentMagnifyingGlassIcon,
  IdentificationIcon,
  BuildingStorefrontIcon,
} from "@heroicons/vue/24/outline";
import type { ErrorMappingKeys, DateRangeType } from "@/types";
import type { uiChartEventData, uiChartPointSelection } from "@/uiTypes";
import DateRange from "@/components/DateRange.vue";

const brandStore = useBrandStore();
const overviewStore = useOverviewStore();

const { getBrandsByAccountId } = storeToRefs(brandStore);
const {
  search_funnel,
  guest_funnel,
  checkin,
  reservation,
  scan,
  previousReservation,
  previousScan,
  previousCheckin,
  checkinSource,
  reservation_errors,
  scan_errors,
  checkin_errors,
} = storeToRefs(overviewStore);

const route = useRoute();
const router = useRouter();
const brand_id = Number(route.params.brand_id);
const account_id = Number(route.params.account_id);
const isReceptionModeEnabled = ref(false);

const explanationTranslations = {
  total: t(`common.total`),
  of: t(`uiStats.of`),
};

const statValuesReservation = computed(() =>
  getStatValues({
    brands: reservation.value,
    previousBrands: previousReservation.value,
    title: t(`uiStats.title.reservation`),
    icon: DocumentMagnifyingGlassIcon,
    showFooter: true,
    actionText: t(`common.viewAll`),
    explanationTranslations,
  })
);
const statValuesScan = computed(() =>
  getStatValues({
    brands: scan.value,
    previousBrands: previousScan.value,
    title: t(`uiStats.title.scan`),
    icon: IdentificationIcon,
    showFooter: true,
    actionText: t(`common.viewAll`),
    explanationTranslations,
  })
);
const statValuesCheckin = computed(() =>
  getStatValues({
    brands: checkin.value,
    previousBrands: previousCheckin.value,
    title: t(`uiStats.title.checkin`),
    icon: BuildingStorefrontIcon,
    showFooter: true,
    actionText: t(`common.viewAll`),
    explanationTranslations,
  })
);
const statValuesCheckinSource = computed(() =>
  getStatValues({
    brands: checkinSource.value,
    title: t(`uiStats.title.checkinSource`),
    icon: BuildingStorefrontIcon,
    changeType: "neutral",
    showFooter: true,
    actionText: t(`common.viewAll`),
    explanationTranslations,
  })
);

const overviewDataRanges = ref(<Record<string, DateRangeType>>{
  globalRange: "",
  selectedRange: "24h",
  search_funnel: "24h",
  guest_funnel: "24h",
  reservation_errors: "24h",
  scan_errors: "24h",
  checkin_errors: "24h",
  scan: "24h",
  checkin: "24h",
  reservation: "24h",
  checkinSource: "24h",
});

const getOverviewGlobal = async (range: DateRangeType) => {
  overviewDataRanges.value.globalRange = ""; //to handle loading of filter stats button
  const selectedBrand = getBrandsByAccountId
    .value(account_id, null)
    .filter((brand) => brand.brandInfo.id === brand_id);
  isReceptionModeEnabled.value = selectedBrand[0].config?.reception_signature
    ? true
    : false;

  await overviewStore.getOverview({
    brand_id,
    range,
    isReceptionModeEnabled: isReceptionModeEnabled.value,
  });
  for (const key in overviewDataRanges.value) {
    overviewDataRanges.value[key] = range;
  }
};

const getScopeChartData = async (
  eventData: uiChartEventData,
  name: ErrorMappingKeys
) => {
  await overviewStore.getParticularChart({
    brand_id,
    name,
    range: eventData.range,
  });
  overviewDataRanges.value[name] = eventData.range;
};
</script>

<template>
  <div>
    <div
      class="actionsAndFilters flex flex-col lg:flex-row w-full justify-between mb-12"
    >
      <div class="flex flex-col lg:flex-row items-center">
        <DateRange
          :global_range="overviewDataRanges.globalRange"
          @range-value="getOverviewGlobal"
        />
      </div>
    </div>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <uiStats
        :loading="reservation ? false : true"
        :item="statValuesReservation"
        @stat-click="router.push({ name: 'reservations' })"
      />
      <uiStats
        :loading="scan ? false : true"
        :item="statValuesScan"
        @stat-click="router.push({ name: 'scans' })"
      />
      <uiStats
        :loading="checkin ? false : true"
        :item="statValuesCheckin"
        @stat-click="router.push({ name: 'precheckins' })"
      />
      <uiStats
        v-if="isReceptionModeEnabled"
        :loading="checkinSource ? false : true"
        :item="statValuesCheckinSource"
        @stat-click="router.push({ name: 'precheckins' })"
      />
    </div>
    <div class="stats grid grid-cols-1 gap-4 mt-4">
      <uiChart
        data-test="ui-chart"
        :loading="reservation ? false : true"
        :series="makeSeries(reservation, overviewDataRanges.reservation)"
        :options="makeOptions()"
        :empty="reservation?.length === 0 ? true : false"
        :title="t('overview.uiChartText.reservation')"
        :range="overviewDataRanges.reservation"
        :dates="
          overviewDataRanges.reservation.from ||
          overviewDataRanges.reservation.to
            ? overviewDataRanges.reservation
            : null
        "
        type="area"
        @selected-range="(eventData:uiChartEventData) => getScopeChartData(eventData, 'reservation')"
        @data-point-selection="(eventData:uiChartPointSelection) => redirectToTable({eventData, tableName: 'reservations', dateRange: overviewDataRanges.reservation})"
      />
      <uiChart
        data-test="ui-chart"
        :loading="scan ? false : true"
        :series="makeSeries(scan, overviewDataRanges.scan)"
        :options="makeOptions()"
        :empty="scan?.length === 0 ? true : false"
        :title="t('overview.uiChartText.scan')"
        :range="overviewDataRanges.scan"
        :dates="
          overviewDataRanges.scan.from || overviewDataRanges.scan.to
            ? overviewDataRanges.scan
            : null
        "
        type="area"
        @selected-range="(eventData:uiChartEventData) => getScopeChartData(eventData, 'scan')"
        @data-point-selection="(eventData:uiChartPointSelection) => redirectToTable({eventData, tableName: 'scans', dateRange: overviewDataRanges.scan})"
      />
      <uiChart
        data-test="ui-chart"
        :loading="checkin ? false : true"
        :series="makeSeries(checkin, overviewDataRanges.checkin)"
        :options="makeOptions()"
        :empty="checkin?.length === 0 ? true : false"
        :title="t('overview.uiChartText.checkin')"
        :range="overviewDataRanges.checkin"
        :dates="
          overviewDataRanges.checkin.from || overviewDataRanges.checkin.to
            ? overviewDataRanges.checkin
            : null
        "
        type="area"
        @selected-range="(eventData:uiChartEventData) => getScopeChartData(eventData, 'checkin')"
        @data-point-selection="(eventData:uiChartPointSelection) => redirectToTable({eventData, tableName: 'precheckins', dateRange: overviewDataRanges.checkin})"
      />
      <uiChart
        v-if="isReceptionModeEnabled"
        data-test="ui-chart"
        :loading="checkinSource ? false : true"
        :series="
          makeSeries(checkinSource, overviewDataRanges.checkinSource, [
            'autocheckin',
            'reception',
          ])
        "
        :options="makeOptions()"
        :empty="checkinSource?.length === 0 ? true : false"
        :title="t('overview.uiChartText.checkinSource')"
        :range="overviewDataRanges.checkinSource"
        :dates="
          overviewDataRanges.checkinSource.from ||
          overviewDataRanges.checkinSource.to
            ? overviewDataRanges.checkinSource
            : null
        "
        type="area"
        @selected-range="(eventData:uiChartEventData) => getScopeChartData(eventData, 'checkinSource')"
        @data-point-selection="(eventData:uiChartPointSelection) => redirectToTable({eventData, tableName: 'precheckins', dateRange: overviewDataRanges.checkinSource, calledFrom: 'checkinSourceChart'})"
      />
    </div>
    <div
      class="stats grid grid-cols-1 lg:grid-cols-1 xl:grid-cols-2 gap-4 mt-4"
    >
      <uiFunnelChartV2
        data-test="ui-funnel"
        :loading="search_funnel ? false : true"
        :options="getFunnelPages(search_funnel)"
        :empty="search_funnel?.length === 0 ? true : false"
        :series="getFunnelSeries(search_funnel)"
        :title="t('overview.uiFunnelChart.searchFunnel')"
        :range="overviewDataRanges.search_funnel"
        @selected-range="(eventData:uiChartEventData) => getScopeChartData(eventData, 'search_funnel')"
      />
      <uiFunnelChartV2
        data-test="ui-funnel"
        :loading="guest_funnel ? false : true"
        :options="getFunnelPages(guest_funnel)"
        :empty="guest_funnel?.length === 0 ? true : false"
        :series="getFunnelSeries(guest_funnel)"
        :title="t('overview.uiFunnelChart.guestFunnel')"
        :range="overviewDataRanges.guest_funnel"
        @selected-range="(eventData:uiChartEventData) => getScopeChartData(eventData, 'guest_funnel')"
      />
      <uiChart
        data-test="ui-chart"
        :loading="reservation_errors ? false : true"
        :series="reservation_errors ? reservation_errors : []"
        :empty="
          reservation_errors &&
          reservation_errors[0] &&
          reservation_errors[0]?.data?.length !== 0
            ? false
            : true
        "
        :options="makeOptions('barType')"
        :title="t('overview.uiChartText.searchErrors')"
        :range="overviewDataRanges.reservation_errors"
        :dates="
          overviewDataRanges.reservation_errors.from ||
          overviewDataRanges.reservation_errors.to
            ? overviewDataRanges.reservation_errors
            : null
        "
        type="bar"
        @selected-range="(eventData:uiChartEventData) => getScopeChartData(eventData, 'reservation_errors')"
        @data-point-selection="(eventData:uiChartPointSelection) => redirectToTable({eventData, tableName: 'reservations', dateRange: overviewDataRanges.reservation_errors, calledFrom: 'errorCharts'})"
      />
      <uiChart
        data-test="ui-chart"
        :loading="scan_errors ? false : true"
        :series="scan_errors ? scan_errors : []"
        :options="makeOptions('barType')"
        :empty="
          scan_errors && scan_errors[0] && scan_errors[0]?.data?.length !== 0
            ? false
            : true
        "
        :title="t('overview.uiChartText.scanErrors')"
        :range="overviewDataRanges.scan_errors"
        :dates="
          overviewDataRanges.scan_errors.from ||
          overviewDataRanges.scan_errors.to
            ? overviewDataRanges.scan_errors
            : null
        "
        type="bar"
        @selected-range="(eventData:uiChartEventData) => getScopeChartData(eventData, 'scan_errors')"
        @data-point-selection="(eventData:uiChartPointSelection) => redirectToTable({eventData, tableName: 'scans', dateRange: overviewDataRanges.scan_errors, calledFrom: 'errorCharts'})"
      />
      <uiChart
        data-test="ui-chart"
        :loading="checkin_errors ? false : true"
        :series="checkin_errors ? checkin_errors : []"
        :options="makeOptions('barType')"
        :empty="
          checkin_errors &&
          checkin_errors[0] &&
          checkin_errors[0]?.data?.length !== 0
            ? false
            : true
        "
        :title="t('overview.uiChartText.checkinErrors')"
        :range="overviewDataRanges.checkin_errors"
        :dates="
          overviewDataRanges.checkin_errors.from ||
          overviewDataRanges.checkin_errors.to
            ? overviewDataRanges.checkin_errors
            : null
        "
        type="bar"
        @selected-range="(eventData:uiChartEventData) => getScopeChartData(eventData, 'checkin_errors')"
        @data-point-selection="(eventData:uiChartPointSelection) => redirectToTable({eventData, tableName: 'precheckins', dateRange: overviewDataRanges.checkin, calledFrom: 'errorCharts'})"
      />
    </div>
  </div>
</template>
