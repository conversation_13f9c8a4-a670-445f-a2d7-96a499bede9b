import { calculateDaysBefore, getCurrentDateInUTC} from "@/helpers/dates";
import type {
	BrandRatioObject,
	UpdateDateRangeBrands,
	ComparisonApiResponse,
} from "@/types";
import { format, subDays } from "date-fns";
import { API } from "@aws-amplify/api";
import { isAbsoluteDateRange } from "@/helpers/uiDateRange";
export const getComparisonRatioStats = async ({
  brandIds,
  name,
  range = "24h",
}: UpdateDateRangeBrands): Promise<{
  current: BrandRatioObject;
  previous: BrandRatioObject;
}> => {

  let date_from: string;
  let date_to: string;
  let previous_date_from: string;

	const { days_before } = calculateDaysBefore(range);

  if (isAbsoluteDateRange(range)) {
		date_from = range.from;
		date_to = `${range.to} 23:59:59`;

		const fromObject = new Date(date_from);
		previous_date_from = format(
			subDays(fromObject, days_before - 1),
			"yyyy-MM-dd",
		);
  } else {
    const currentDateUTC = getCurrentDateInUTC();
    const date_format = days_before === 1 ? "yyyy-MM-dd HH:mm:ss" : "yyyy-MM-dd";

    date_to = format(currentDateUTC, "yyyy-MM-dd HH:mm:ss");
    date_from = format(subDays(currentDateUTC, days_before), date_format);
    previous_date_from = format(subDays(currentDateUTC, days_before * 2), date_format);

  }
  const response = await getRatioStats(
    brandIds,
    name,
    date_from,
    date_to,
    previous_date_from,
  );

  const toReturn = {
    current: {
      reservation: response.current.reservation?.length
        ? response.current.reservation
        : [],
      scan: response.current.scan?.length ? response.current.scan : [],
      checkin: response.current.checkin?.length ? response.current.checkin : [],
    },
    previous: {
      reservation: response.previous.reservation?.length
        ? response.previous.reservation
        : [],
      scan: response.previous.scan?.length ? response.previous.scan : [],
      checkin: response.previous.checkin?.length
        ? response.previous.checkin
        : [],
    },
  };
  
  return toReturn;
};

export const getRatioStats = async (
	brandIds: number[],
	name: string | undefined,
	date_from: string,
	date_to: string,
	previous_date_from: string,
): Promise<ComparisonApiResponse> => {
	try {
		if (!brandIds.length) {
			throw new Error("no brands were selected");
		}

		const queryParams = {
			brand_ids: brandIds.join(","),
			name,
			date_from,
			date_to,
			previous_date_from,
		};
		const brandsRatioData = await API.get(
			"stats",
			"autocheckin/brands/getBrandsStats",
			{
				queryStringParameters: queryParams,
			},
		);		
		return brandsRatioData.data;
	} catch (error) {
		throw new Error(`getBrandsRatio request failed: => ${error}`);
	}
};
