import authView from "./AuthView.vue";
import { describe, it, vi, expect } from "vitest";
import { mount } from "@vue/test-utils";
import flushPromises from "flush-promises";
import { beforeEach } from "vitest";
import { createPinia, setActivePinia } from "pinia";
import { useUserStore } from "@/stores/user";
import { useNotificationStore } from "@/stores/notification";
import { t } from "@/locales";
import { useRouter } from "vue-router";

vi.mock("vue-router", async () => {
	const actual = await vi.importActual("vue-router");
	return {
		...actual,
		useRouter: vi.fn(() => ({
			push: () => {},
		})),
	};
});

describe("AuthView Tests", () => {
	beforeEach(() => {
		vi.mock("@vueuse/router", () => ({
			useRouteQuery: vi.fn(() => ref()),
		}));
		setActivePinia(createPinia());
	});

	it("renders all components when the view is mounted", async () => {
		const wrapper = await mount(authView);
		await flushPromises();

		const emailInput = wrapper.find('[data-test="emailInput"]');
		const passwordInput = wrapper.find('[data-test="passwordInput"]');
		const logInButton = wrapper.find('[data-test="logInButton"]');
		const resetPasswordButton = wrapper.find(
			'[data-test="resetPasswordButton"]',
		);

		expect(wrapper.exists()).toBe(true);
		expect(emailInput.isVisible()).toBe(true);
		expect(passwordInput.isVisible()).toBe(true);
		expect(logInButton.isVisible()).toBe(true);
		expect(resetPasswordButton.isVisible()).toBe(true);
	});

	it("validates inputs when login button is clicked without filling inputs", async () => {
		const wrapper = await mount(authView);
		await flushPromises();

		const logInButton = wrapper.find('[data-test="logInButton"]');
		const emailInput = wrapper.find('[data-test="emailInput"]');
		const passwordInput = wrapper.find('[data-test="passwordInput"]');

		expect(emailInput.attributes().color).toBe("");
		expect(emailInput.attributes().error).toBe("");
		expect(passwordInput.attributes().color).toBe("");
		expect(passwordInput.attributes().error).toBe("");

		await logInButton.trigger("click");
		await flushPromises();

		expect(emailInput.attributes().color).toBe("danger");
		expect(emailInput.attributes().error).toBe(t("auth.emailInputError"));
		expect(passwordInput.attributes().color).toBe("danger");
		expect(passwordInput.attributes().error).toBe(t("auth.passwordInputError"));
	});

	it("opens the reset password modal when reset password button is clicked", async () => {
		const wrapper = await mount(authView);
		await flushPromises();

		const resetPasswordButton = wrapper.find(
			'[data-test="resetPasswordButton"]',
		);

		expect(wrapper.vm.openResetPasswordModal).toBe(false);

		await resetPasswordButton.trigger("click");

		expect(wrapper.vm.openResetPasswordModal).toBe(true);
	});

	it("handles forgotPassword submission correctly", async () => {
		const push = vi.fn();
		useRouter.mockImplementationOnce(() => ({
			push,
		}));
		const userStore = useUserStore();
		const notificationStore = useNotificationStore();
		userStore.forgotPassword = vi.fn().mockResolvedValue(null);

		const notificationError = vi.spyOn(notificationStore, "error");

		const wrapper = await mount(authView);
		wrapper.vm.state.email = "<EMAIL>";
		await flushPromises();

		// Test user not found
		userStore.error = "UserNotFoundException";
		wrapper.vm.forgotPassword();
		await flushPromises();
		expect(userStore.forgotPassword).toHaveBeenCalledWith("<EMAIL>");
		expect(notificationError).toHaveBeenCalledWith(
			t("auth.userNotFoundError.title"),
			t("auth.userNotFoundError.body"),
		);

		// Test limit attepts exception
		userStore.error = "LimitExceededException";
		wrapper.vm.forgotPassword();
		await flushPromises();
		expect(userStore.forgotPassword).toHaveBeenCalledWith("<EMAIL>");
		expect(notificationError).toHaveBeenCalledWith(
			t("newPassword.attemptsError.title"),
			t("newPassword.attemptsError.body"),
		);

		// Test default exception
		userStore.error = "DefaultException";
		wrapper.vm.forgotPassword();
		await flushPromises();
		expect(userStore.forgotPassword).toHaveBeenCalledWith("<EMAIL>");
		expect(notificationError).toHaveBeenCalledWith(
			t("auth.defaultError.title"),
			t("auth.defaultError.body"),
		);

		// Test forgotPassword correctly
		userStore.error = "";
		wrapper.vm.forgotPassword();
		await flushPromises();
		expect(userStore.forgotPassword).toHaveBeenCalledWith("<EMAIL>");
		expect(push).toHaveBeenCalledTimes(1);
		expect(push).toHaveBeenCalledWith({ name: "newPassword" });
	});

	it("displays error notification when login fails", async () => {
		const userStore = useUserStore();
		const notificationStore = useNotificationStore();
		userStore.login = vi.fn().mockResolvedValueOnce(null);
		userStore.error = "defaultError";

		const notificationError = vi.spyOn(notificationStore, "error");

		const wrapper = await mount(authView);
		wrapper.vm.state.email = "<EMAIL>";
		wrapper.vm.state.password = "password123";
		await flushPromises();

		await wrapper.find('[data-test="logInButton"]').trigger("click");
		await flushPromises();

		expect(notificationError).toHaveBeenCalledWith(
			t("auth.defaultError.title"),
			t("auth.defaultError.body"),
		);
	});

	it("opens new password modal when NewPasswordRequiredException is thrown", async () => {
		const userStore = useUserStore();
		userStore.login = vi.fn().mockResolvedValueOnce(null);
		userStore.error = "NewPasswordRequiredException";

		const wrapper = await mount(authView);
		wrapper.vm.state.email = "<EMAIL>";
		wrapper.vm.state.password = "password123";
		await flushPromises();

		const logInButton = wrapper.find('[data-test="logInButton"]');

		await logInButton.trigger("click");
		await flushPromises();

		expect(wrapper.vm.openNewPasswordModal).toBe(true);
	});

	it("navigates to MFA configuration when user needs MFA setup", async () => {
		const push = vi.fn();
		useRouter.mockImplementationOnce(() => ({
			push,
		}));
		const userStore = useUserStore();

		userStore.login = vi.fn().mockResolvedValueOnce(null);
		userStore.needMfaSetup = true;

		const wrapper = await mount(authView);
		wrapper.vm.state.email = "<EMAIL>";
		wrapper.vm.state.password = "password123";
		await flushPromises();

		const logInButton = wrapper.find('[data-test="logInButton"]');

		await logInButton.trigger("click");
		await flushPromises();

		expect(push).toHaveBeenCalledTimes(1);
		expect(push).toHaveBeenCalledWith({ name: "MfaConfigure" });
	});

	it("opens MFA modal when SoftwareTokenMfaException is thrown", async () => {
		const userStore = useUserStore();
		userStore.login = vi.fn().mockResolvedValueOnce(null);
		userStore.error = "SoftwareTokenMfaException";

		const wrapper = await mount(authView);
		wrapper.vm.state.email = "<EMAIL>";
		wrapper.vm.state.password = "password123";
		await flushPromises();

		expect(wrapper.vm.openMfaModal).toBe(false);

		const logInButton = wrapper.find('[data-test="logInButton"]');
		await logInButton.trigger("click");
		await flushPromises();

		expect(wrapper.vm.openMfaModal).toBe(true);
	});

	it("handles MFA code submission correctly", async () => {
		const userStore = useUserStore();
		const notificationStore = useNotificationStore();

		const notificationError = vi.spyOn(notificationStore, "error");
		const notificationSuccess = vi.spyOn(notificationStore, "success");

		const wrapper = await mount(authView);

		// Test bad code inserted
		userStore.validateMfaCode = vi.fn().mockResolvedValueOnce(null);
		wrapper.vm.validationCode = "123456";
		wrapper.vm.handleMfaCode();
		await flushPromises();

		expect(userStore.validateMfaCode).toHaveBeenCalledWith("123456");
		expect(notificationError).toHaveBeenCalledWith(
			t("mfaConfigure.notification.error.title"),
			t("mfaConfigure.notification.error.message"),
		);

		// Test correct code inserted
		userStore.validateMfaCode = vi.fn().mockResolvedValueOnce("Success");
		wrapper.vm.handleMfaCode();
		await flushPromises();

		expect(userStore.validateMfaCode).toHaveBeenCalledWith("123456");

		expect(notificationSuccess).toHaveBeenCalledWith(
			t("auth.isAuth.title"),
			t("auth.isAuth.body"),
		);
	});
});
