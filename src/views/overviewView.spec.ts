import overviewView from "./OverviewView.vue";
import { defineComponent } from "vue";
import { describe, it, vi, expect } from "vitest";
import { mount } from "@vue/test-utils";
import flushPromises from "flush-promises";
import { getOverviewMock } from "../../mocks/modules/Overview/data";
import { calculateErrors } from "@/helpers/uiChart";
import { useUserStore } from "@/stores/user";
import { createPinia } from "pinia";
import brand from "@/helpers/brand";

// describe("overviewView", () => {
// it("component renders correctly if cognito session has access to that brand", async () => {
// 	const pinia = createPinia();
// 	const user = useUserStore(pinia);
// 	brand.currentBrand = vi.fn(() => 76);
// 	user.$state.brandsAllowed = [76, 1];
// 	// Substituting the module where the api call is with the mock function
// 	vi.mock("../repositories/Overview/overview", async () => {
// 		return {
// 			default: {
// 				get: async () => getOverviewMock(),
// 			},
// 		};
// 	});
// 	const TestComponent = defineComponent({
// 		components: { overviewView },
// 		template: "<div><overviewView/></div>",
// 	});
// 	const wrapper = await mount(TestComponent);
// 	await flushPromises();
// 	const uiChart = await wrapper.find('[data-test="ui-chart"]');
// 	const uiFunnel = await wrapper.find('[data-test="ui-funnel"]');
// 	expect(wrapper.exists()).toBe(true);
// 	expect(uiChart.exists()).toBe(true);
// 	expect(uiChart.isVisible()).toBe(true);
// 	expect(uiFunnel.exists()).toBe(true);
// 	expect(uiFunnel.isVisible()).toBe(true);
// });
// it("component don't renders if cognito session has no access", async () => {
// 	const pinia = createPinia();
// 	const user = useUserStore(pinia);
// 	brand.currentBrand = vi.fn(() => 2);
// 	user.$state.brandsAllowed = [76, 1];
// 	vi.mock("../repositories/Overview/overview", async () => {
// 		return {
// 			default: {
// 				get: async () => getOverviewMock(),
// 			},
// 		};
// 	});
// 	const TestComponent = defineComponent({
// 		components: { overviewView },
// 		template: "<Suspense><overviewView/></Suspense>",
// 	});
// 	const wrapper = await mount(TestComponent);
// 	await flushPromises();
// 	const uiChart = await wrapper.find('[data-test="ui-chart"]');
// 	const uiFunnel = await wrapper.find('[data-test="ui-funnel"]');
// 	expect(wrapper.exists()).toBe(true);
// 	expect(uiChart.exists()).toBe(false);
// 	expect(uiFunnel.exists()).toBe(false);
// });
// });

describe("calculateErrors", () => {
	it("should return the correct error count for given data", () => {
		const data = [
			{
				time: "2023-07-01 00:00:00",
				total: 18,
				success: 10,
				error: {
					count: 8,
					types: [
						{ error: "Error1", total: 5 },
						{ error: "Error2", total: 3 },
					],
				},
			},
			{
				time: "2023-07-01 01:00:00",
				total: 12,
				success: 9,
				error: {
					count: 3,
					types: [
						{ error: "Error1", total: 2 },
						{ error: "Error3", total: 1 },
					],
				},
			},
		];

		const result = calculateErrors(data);

		// Define your expected result based on the input data
		const expectedResult = [
			{
				name: "Error Count",
				data: [
					{ x: "Error1", y: 7 },
					{ x: "Error2", y: 3 },
					{ x: "Error3", y: 1 },
				],
			},
		];

		expect(result).toEqual(expectedResult);
	});
});
