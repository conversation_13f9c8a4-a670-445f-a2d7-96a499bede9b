import auth from "./Auth";
import brands from "./Brands";
import comparison from "./Comparison";
import dashboard from "./Dashboard";
import overview from "./Overview";
import sidebar from "./Sidebar";
import mfaConfigure from "./MfaConfigure";
import filterTable from "./components/filterTable";
import errorMessageModal from "./components/errorMessageModal";
import errorMessages from "./errorMessages";
import funnelPages from "./funnelPages";
import newPassword from "./NewPassword";
import common from "./Common";
import uiStats from "./uiStats";
import title from "./title";
import editUserModal from "./components/editUserModal";
import userCreateView from "./components/userCreateView";
import confirmActionModal from "./components/confirmActionModal";

export default {
	errorMessages,
	funnelPages,
	overview,
	dashboard,
	sidebar,
	mfaConfigure,
	filterTable,
	errorMessageModal,
	auth,
	brands,
	comparison,
	newPassword,
	common,
	uiStats,
	title,
	editUserModal,
	userCreateView,
	confirmActionModal,
};
