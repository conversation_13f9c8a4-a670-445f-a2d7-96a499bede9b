import { setActive<PERSON>inia, createPinia } from "pinia";
import { Auth } from "aws-amplify";
import { useUserStore } from "@/stores/user";
import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { login } from "../modules/Auth/Auth";

vi.mock("aws-amplify", () => ({
	Auth: {
		currentAuthenticatedUser: vi.fn(),
		signIn: vi.fn(),
		completeNewPassword: vi.fn(),
		signOut: vi.fn(),
		forgotPassword: vi.fn(),
		forgotPasswordSubmit: vi.fn(),
		currentSession: vi.fn().mockResolvedValue({
			getIdToken: () => ({
				payload: { "cognito:groups": ["brand_admin"] },
			}),
		}),
	},
}));

vi.mock("../modules/Auth/Auth", () => {
	return {
		login: vi.fn(),
	};
});

describe("User Store", () => {
	let store;

	beforeEach(() => {
		setActivePinia(createPinia());
		store = useUserStore();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it("load - should set the user and authentication status if the user is authenticated", async () => {
		const user = { username: "testuser" };
		Auth.currentAuthenticatedUser.mockResolvedValueOnce(user);

		await store.load();

		expect(store.user).toStrictEqual(user);
		expect(store.isAuthenticated).toBe(true);
	});

	it("load - should reset the user and log the error if the user is not authenticated", async () => {
		const error = new Error("Authentication error");
		Auth.currentAuthenticatedUser.mockRejectedValueOnce(error);

		await store.load();

		expect(store.user).toStrictEqual({});
		// expect(console.warn).toHaveBeenCalledWith(error);
		expect(store.isAuthenticated).toBe(false);
	});

	it("login - should set the user and authentication status after successful login", async () => {
		const user = {
			username: "testuser",
			attributes: { "custom:brand_id": "1,2,3" },
			roles: ["brand_admin"],
		};

		const userData = { email: "<EMAIL>", password: "password" };

		login.mockResolvedValue(user);

		await store.login(userData);
		expect(store.user).toStrictEqual(user);
		expect(store.isAuthenticated).toBe(true);
	});

	it("login - should set error to error code after failed login", async () => {
		const error = { code: "Login error" };
		const userData = { email: "<EMAIL>", password: "password" };

		login.mockRejectedValue(error);

		await store.login(userData);
		expect(store.error).toBe("Login error");
	});

	it("completeNewPassword - should set the user after successfully completing new password", async () => {
		const user = {
			username: "testuser",
			attributes: { "custom:brand_id": "1,2,3" },
			roles: ["brand_admin"],
		};
		const password = "newpassword";
		store.user = { attributes: { email: "<EMAIL>" } };
		Auth.completeNewPassword.mockResolvedValue(user);

		login.mockResolvedValue(user);

		await store.completeNewPassword(password);

		expect(login).toHaveBeenCalledOnce();
		expect(store.user).toStrictEqual(user);
	});
	it('should set the error to "defaultError" when user roles are not present', async () => {
		login.mockResolvedValue({
			email: "<EMAIL>",
			password: "password",
			// No roles property
		});
		await store.login({ email: "<EMAIL>", password: "password" });
		expect(store.error).toBe("defaultError");
	});

	it("completeNewPassword - should set error to error code after failing to complete new password", async () => {
		const error = { code: "Complete new password error" };
		const password = "newpassword";
		Auth.completeNewPassword.mockRejectedValue(error);

		await store.completeNewPassword(password);
		expect(store.error).toBe("Complete new password error");
	});

	it("logout - should reset the user and authentication status after successful logout", async () => {
		Auth.signOut.mockResolvedValue({});

		await store.logout();

		expect(store.isAuthenticated).toBe(false);
		expect(store.user).toEqual({});
	});

	it("logout - should set error to error code after failing to logout", async () => {
		const error = { code: "Logout error" };
		Auth.signOut.mockRejectedValue(error);

		await store.logout();
		expect(store.error).toBe("Logout error");
	});

	it("forgotPassword - should resolve with data after successfully initiating password reset", async () => {
		const data = { resetCode: "123456" };
		const username = "testuser";
		Auth.forgotPassword.mockResolvedValue(data);

		const result = await store.forgotPassword(username);

		expect(result).toBe(data);
	});

	it("forgotPassword - should set error with error code after failing to initiate password reset", async () => {
		const error = { code: "Forgot password error" };
		const username = "testuser";
		Auth.forgotPassword.mockRejectedValue(error);

		await store.forgotPassword(username);
		expect(store.error).toBe("Forgot password error");
	});

	it("forgotPasswordSubmit - should resolve with data after successfully submitting password reset", async () => {
		const data = { resetCode: "123456" };
		const userData = {
			email: "<EMAIL>",
			code: "123456",
			password: "newpassword",
		};
		Auth.forgotPasswordSubmit.mockResolvedValue(data);

		const result = await store.forgotPasswordSubmit(userData);

		expect(result).toBe(data);
	});

	it("forgotPasswordSubmit - should set error with error code after failing to submit password reset", async () => {
		const error = { code: "Forgot password submit error" };
		const userData = {
			email: "<EMAIL>",
			code: "123456",
			password: "newpassword",
		};
		Auth.forgotPasswordSubmit.mockRejectedValue(error);

		await store.forgotPasswordSubmit(userData);
		expect(store.error).toBe("Forgot password submit error");
	});
});
