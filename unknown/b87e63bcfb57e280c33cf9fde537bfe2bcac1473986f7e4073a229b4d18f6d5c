import { createI18n } from "vue-i18n";
import es from "./es";
import en from "./en";
const messages = {
	en,
	es,
};

const posibleMessages = ["en", "es"];
const spainLanguages = ["ca", "eu", "gl"];

// Check user browser language
const userLocale = window.navigator.language || "es";

const getLocale = (lang: string) => {
	// If user's device language is catalan, galician or basque, use spanish
	if (spainLanguages.includes(lang)) {
		return "es";
	}
	return posibleMessages.includes(lang) ? lang : "en";
};
const locale = getLocale(userLocale.substring(0, 2));
const fallbackLocale = import.meta.env.VITE_I18N_FALLBACK_LOCALE;

export const i18n = createI18n({
	legacy: false,
	// globalInjection: true,
	locale,
	fallbackLocale,
	// silentTranslationWarn: true,
	messages,
	allowComposition: true,
});

export const { t } = i18n.global;
