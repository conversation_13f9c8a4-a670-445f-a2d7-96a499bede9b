export default {
	header: "Create a new password",
	code<PERSON><PERSON><PERSON>: "Code",
	confirm<PERSON>abel: "Confirm the password",
	changePasswordButton: "Change password",
	codeInputError: "Error in code",
	codeInputEmpty: "The field cannot be empty",
	alternativeText: "Or you can also",
	returnToLogin: "Return to login page",
	newCode: {
		header: "New code",
		body: "Enter your email address. We will send you a new code to reset your password.",
		resetButton: "Send code",
	},
	codeError: {
		title: "Invalid code",
		body: "Invalid verificaction code provided, please try again.",
	},
	attemptsError: {
		title: "Attempt limit exceeded.",
		body: "Attempt limit exceeded, please try after some time.",
	},
	expiredError: {
		title: "Expired code",
		body: "Expired code provided, please request a code again.",
	},
	passwordMatch: "Passwords must match",
};
