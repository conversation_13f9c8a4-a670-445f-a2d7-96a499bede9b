<script setup lang="ts">
import { computed, ref, nextTick } from "vue";
import { useRoute } from "vue-router";
import { useComparisonStore } from "@/stores/comparison";
import { useBrandStore } from "@/stores/brand";
import { storeToRefs } from "pinia";
import type { DateRangeType, BrandRatioObject } from "@/types";
import type { uiCheckBoxEvent, uiChartPointSelection } from "@/uiTypes";
import type { uiChartSelectedRangeEvent } from "@/uiTypes";
import {
  DocumentMagnifyingGlassIcon,
  IdentificationIcon,
  BuildingStorefrontIcon,
  FunnelIcon,
} from "@heroicons/vue/24/outline";
import { t } from "@/locales";
import { getStatValues } from "@/helpers/uiStats";
import {
  getSerie,
  getSerieParams,
  getBrandNames,
  getOptions,
  redirectFromComparisonToTable,
} from "@/helpers/uiChart";
import { calculateHeight } from "@/helpers/comparison";
import { getItemsWithStructure } from "@/helpers/uiRightSidebar";
import DateRange from "@/components/DateRange.vue";

const brandStore = useBrandStore();
const comparisonStore = useComparisonStore();

const route = useRoute();
const accountId = Number(route.params.account_id);

const {
  reservation,
  scan,
  checkin,
  previousReservation,
  previousScan,
  previousCheckin,
} = storeToRefs(comparisonStore);
const { getBrandsByAccountId } = storeToRefs(brandStore);

const brandIdsSelected = ref<number[]>([]);
const openRightSidebar = ref(false);
const optionsReservation = ref({});
const optionsScan = ref({});
const optionsCheckin = ref({});
const renderComponent = ref(true);
const originalBrands = getBrandsByAccountId.value(accountId, null);
const brandIds = ref(<number[]>[]);
const brandsReservation = ref(<number[]>[]);
const brandsScan = ref(<number[]>[]);
const brandsCheckin = ref(<number[]>[]);

originalBrands.forEach(({ brandInfo: { id } }) => {
  brandIds.value.push(id);
  brandsReservation.value.push(id);
  brandsScan.value.push(id);
  brandsCheckin.value.push(id);
});

const uiChartHeight = ref(calculateHeight(brandIds.value.length));
const uiCheckBoxItem = ref(getItemsWithStructure(originalBrands));

const comparisonDateRanges = ref(<Record<string, DateRangeType>>{
  globalRange: "",
  scan: "24h",
  reservation: "24h",
  checkin: "24h",
});

const chartReservationSerie = computed(() =>
  getSerie(getSerieParams(reservation.value))
);
const chartScanSerie = computed(() => getSerie(getSerieParams(scan.value)));
const chartCheckinSerie = computed(() =>
  getSerie(getSerieParams(checkin.value))
);

const explanationTranslations = {
  total: t(`common.total`),
  of: t(`uiStats.of`),
};

const statValuesReservation = computed(() =>
  getStatValues({
    brands: reservation.value,
    previousBrands: previousReservation.value,
    title: t(`uiStats.title.reservation`),
    icon: DocumentMagnifyingGlassIcon,
    explanationTranslations,
  })
);

const statValuesScan = computed(() =>
  getStatValues({
    brands: scan.value,
    previousBrands: previousScan.value,
    title: t(`uiStats.title.scan`),
    icon: IdentificationIcon,
    explanationTranslations,
  })
);

const statValuesCheckin = computed(() =>
  getStatValues({
    brands: checkin.value,
    previousBrands: previousCheckin.value,
    title: t(`uiStats.title.checkin`),
    icon: BuildingStorefrontIcon,
    explanationTranslations,
  })
);

const getComparisonGlobal = async (range: DateRangeType) => {
  comparisonDateRanges.value.globalRange = "";
  await getRatioData({ range });
  await getChartOptions();
  for (const key in comparisonDateRanges.value) {
    comparisonDateRanges.value[key] = range;
  }
};

const getSingleChartData = async (
  event: uiChartSelectedRangeEvent
): Promise<void> => {
  await getRatioData({ range: event.range, name: event.chartId });
  comparisonDateRanges.value[event.chartId] = event.range;
};

const getRatioData = async (
  options: {
    range?: DateRangeType;
    name?: keyof BrandRatioObject;
  } = {}
): Promise<void> => {
  const { range, name } = options;
  const brands = brandIdsSelected.value.length
    ? brandIdsSelected.value
    : brandIds.value;
  await comparisonStore.getComparisonStats({ brandIds: brands, range, name });
  getChartOptions();
};

const getChartOptions = async (): Promise<void> => {
  brandsReservation.value = brandIdsSelected.value.length
    ? brandIdsSelected.value
    : brandsReservation.value;

  brandsScan.value = brandIdsSelected.value.length
    ? brandIdsSelected.value
    : brandsScan.value;

  brandsCheckin.value = brandIdsSelected.value.length
    ? brandIdsSelected.value
    : brandsCheckin.value;
  const reservationNames = getBrandNames(
    brandsReservation.value,
    originalBrands,
    reservation.value
  );
  const scanNames = getBrandNames(brandsScan.value, originalBrands, scan.value);
  const checkinNames = getBrandNames(
    brandsCheckin.value,
    originalBrands,
    checkin.value
  );

  optionsReservation.value = getOptions(reservationNames);
  optionsScan.value = getOptions(scanNames);
  optionsCheckin.value = getOptions(checkinNames);
};

const uiCheckBoxHandleClick = (event: uiCheckBoxEvent): void => {
  const isChecked = event.checked;
  const brandId = parseInt(event.value);
  const brand = uiCheckBoxItem.value.find(
    (brand) => Number(brand.id) === brandId
  );

  if (isChecked) {
    if (!brandIdsSelected.value.includes(brandId)) {
      brandIdsSelected.value.push(brandId);
      brand && (brand.check = true);
    }
  } else {
    const index = brandIdsSelected.value.indexOf(brandId);
    if (index !== -1) {
      brandIdsSelected.value.splice(index, 1);
      brand && (brand.check = false);
    }
  }
};

const updateHeight = async (): Promise<void> => {
  renderComponent.value = false;
  const selectedBrandIds = brandIdsSelected.value.length
    ? brandIdsSelected.value
    : brandIds.value;
  uiChartHeight.value = await calculateHeight(selectedBrandIds.length);
  await nextTick();
  renderComponent.value = true;
};

const handleCloseRightSideBar = async (): Promise<void> => {
  openRightSidebar.value = false;
  await getComparisonGlobal(comparisonDateRanges.value.globalRange);
  await updateHeight();
};

const handleOpenRightSideBar = (): void => {
  uiCheckBoxItem.value.forEach((brand) => {
    if (!brandIdsSelected.value.length) {
      brand.check = false;
    } else {
      if (brandIdsSelected.value.includes(Number(brand.id))) {
        brand.check = true;
      }
    }
  });
  openRightSidebar.value = true;
};

const handleResetCheckedBrands = (): void => {
  uiCheckBoxItem.value.forEach((brand) => (brand.check = false));
  brandIdsSelected.value = [];
};
</script>
<template>
  <div>
    <uiRightSidebar
      :open="openRightSidebar"
      :title="t('comparison.uiRightSidebar.title')"
      :description="t('comparison.uiRightSidebar.description')"
      @close-right-bar="handleCloseRightSideBar"
    >
      <header class="flex justify-between mb-4 items-center">
        <h2 class="font-bold">{{ t("comparison.filter.brand") }}</h2>
        <span
          class="text-red-600 hover:text-red-900 cursor-pointer text-xs"
          @click="handleResetCheckedBrands"
          >{{ t("comparison.filter.restore") }}</span
        >
      </header>
      <uiCheckbox
        v-for="item in uiCheckBoxItem"
        :key="item.id"
        class="mb-2"
        :loading="false"
        :check="item.check"
        :value="item.id"
        @checkbox-changed="uiCheckBoxHandleClick"
      >
        {{ item.name }}
      </uiCheckbox>
    </uiRightSidebar>
    <div
      class="actionsAndFilters flex flex-col lg:flex-row w-full justify-between mb-12"
    >
      <div class="flex flex-col lg:flex-row items-center">
        <DateRange
          :global_range="comparisonDateRanges.globalRange"
          @range-value="getComparisonGlobal"
        />
      </div>
      <div class="flex-col items-center">
        <uiButton
          class="w-full mt-4 lg:w-auto lg:mt-0 lg:ml-4"
          :icon="FunnelIcon"
          :loading="comparisonDateRanges.globalRange ? false : true"
          @click="handleOpenRightSideBar"
          >{{ t("comparison.filter.stats") }}</uiButton
        >
      </div>
    </div>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <uiStats
        :loading="
          reservation.length && previousReservation.length ? false : true
        "
        :item="statValuesReservation"
      />
      <uiStats
        :loading="scan.length && previousScan.length ? false : true"
        :item="statValuesScan"
      />
      <uiStats
        :loading="checkin.length && previousCheckin.length ? false : true"
        :item="statValuesCheckin"
      />
    </div>
    <div class="stats grid grid-cols-1 gap-4 mt-4">
      <uiChart
        v-if="renderComponent"
        id="reservation"
        :height="uiChartHeight"
        :title="t('comparison.uiChart.title.reservation')"
        :options="optionsReservation"
        :series="chartReservationSerie"
        :loading="reservation.length ? false : true"
        :horizontal="true"
        :stacked="true"
        :range="comparisonDateRanges.reservation"
        type="bar"
        @selected-range="getSingleChartData"
        @data-point-selection="(eventData:uiChartPointSelection) => redirectFromComparisonToTable
        (eventData, 'reservations', comparisonDateRanges.reservation, brandsReservation)"
      />
    </div>
    <div
      class="stats grid grid-cols-1 lg:grid-cols-1 xl:grid-cols-2 gap-4 mt-4"
    >
      <uiChart
        v-if="renderComponent"
        id="scan"
        :height="uiChartHeight"
        :title="t('comparison.uiChart.title.scan')"
        :options="optionsScan"
        :series="chartScanSerie"
        :loading="scan.length ? false : true"
        :horizontal="true"
        :stacked="true"
        :range="comparisonDateRanges.scan"
        type="bar"
        @selected-range="getSingleChartData"
        @data-point-selection="(eventData:uiChartPointSelection) => redirectFromComparisonToTable
        (eventData, 'scans', comparisonDateRanges.scan, brandsScan)"
      />
      <uiChart
        v-if="renderComponent"
        id="checkin"
        :height="uiChartHeight"
        :title="t('comparison.uiChart.title.checkin')"
        :options="optionsCheckin"
        :series="chartCheckinSerie"
        :loading="checkin.length ? false : true"
        :horizontal="true"
        :stacked="true"
        :range="comparisonDateRanges.checkin"
        type="bar"
        @selected-range="getSingleChartData"
        @data-point-selection="(eventData:uiChartPointSelection) => redirectFromComparisonToTable
        (eventData, 'precheckins', comparisonDateRanges.checkin, brandsCheckin)"
      />
    </div>
  </div>
</template>
