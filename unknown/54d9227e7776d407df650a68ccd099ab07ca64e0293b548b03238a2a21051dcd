import { describe, it, beforeEach, vi, afterEach, expect } from "vitest";
import { createPinia, setActivePinia } from "pinia";
import { useComparisonStore } from "@/stores/comparison";
import comparisonMock from "@mocks/modules/Comparison/comparisonMock.json";
import { getComparisonRatioStats } from "@/modules/Comparison/Comparison";

vi.mock("@/modules/Comparison/Comparison.ts", () => ({
	getComparisonRatioStats: vi.fn(),
}));
describe("Comparison Store", () => {
	// rome-ignore lint/suspicious/noExplicitAny: <>
	let store: any;
	beforeEach(() => {
		setActivePinia(createPinia());
		store = useComparisonStore();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});
	it("should save correctly on state data the response", async () => {
		getComparisonRatioStats.mockResolvedValue(comparisonMock);
		const params = {
			brandIds: [2, 76, 83],
		};
		await store.getComparisonStats(params);
		expect(store.reservation).toEqual(comparisonMock.current.reservation);
		expect(store.scan).toEqual(comparisonMock.current.scan);
		expect(store.checkin).toEqual(comparisonMock.current.checkin);
		expect(store.error).toEqual("");
	});
	it("should save error correctly on state if getComparisonRatioStats throws error", async () => {
		getComparisonRatioStats.mockRejectedValue(
			new Error("getBrandsRatio request failed: => no brands were selected"),
		);
		const params = {
			brandIds: [],
		};

		await store.getComparisonStats(params);
		expect(store.reservation).toEqual([]);
		expect(store.scan).toEqual([]);
		expect(store.checkin).toEqual([]);
		expect(store.error).toBe(
			"getBrandsRatio request failed: => no brands were selected",
		);
	});
	it("should save error correctly on state if no brandId was set", async () => {
		const params = {
			brandIds: [],
		};

		await store.getComparisonStats(params);
		expect(store.reservation).toEqual([]);
		expect(store.scan).toEqual([]);
		expect(store.checkin).toEqual([]);
		expect(store.error).toBe(
			"getBrandsRatio request failed: => no brands were selected",
		);
	});
	it("should throw error if invalid name was set", async () => {
		getComparisonRatioStats.mockResolvedValue(comparisonMock.data);
		const params = {
			brandIds: [76, 83, 2],
			name: "invalidName",
		};
		await store.getComparisonStats(params);
		expect(store.reservation).toEqual([]);
		expect(store.scan).toEqual([]);
		expect(store.checkin).toEqual([]);
		expect(store.error).toBe(
			"getBrands action failed: => Error: Invalid 'name' param",
		);
	});
	it("should save response only on it's state if name is set", async () => {
		getComparisonRatioStats.mockResolvedValue(comparisonMock);
		const params = {
			brandIds: [76, 83, 2],
			name: "reservation",
		};
		await store.getComparisonStats(params);
		expect(store.reservation).toEqual(comparisonMock.current.reservation);
		expect(store.scan).toEqual([]);
		expect(store.checkin).toEqual([]);
		expect(store.error).toBe("");
	});
	it("should reset to default values if it has a value on a single state", async () => {
		getComparisonRatioStats.mockResolvedValue(comparisonMock);
		const params = {
			brandIds: [76, 83, 2],
			name: "reservation",
		};
		await store.getComparisonStats(params);
		expect(store.reservation).toEqual(comparisonMock.current.reservation);
		expect(store.scan).toEqual([]);
		expect(store.checkin).toEqual([]);
		expect(store.error).toBe("");
		await store.reset();
		expect(store.reservation).toEqual([]);
	});
	it("should reset to default values on all states", async () => {
		getComparisonRatioStats.mockResolvedValue(comparisonMock.data);
		const params = {
			brandIds: [76, 83, 2],
		};
		store.error = "some random error";
		await store.getComparisonStats(params);

		await store.reset();
		expect(store.reservation).toEqual([]);
		expect(store.scan).toEqual([]);
		expect(store.checkin).toEqual([]);
		expect(store.error).toBe("");
	});
});
