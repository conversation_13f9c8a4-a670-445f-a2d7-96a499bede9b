import { describe, it, expect, vi } from "vitest";
import { getSerie, getSerieParams, sortData } from "./uiChart";
import type { uiChartGetSerieParams } from "@/uiTypes";
import type { BrandRatio, SerieData } from "@/types";
describe("uiChart.ts functions", () => {
	vi.mock("@/locales", () => ({
		t: (key: string) => {
			if (key === "common.error") {
				return "Error";
			}
			if (key === "common.success") {
				return "Success";
			}
		},
	}));
	// getSerie function
	it("should return an array of SerieData with success and error data", () => {
		const params: uiChartGetSerieParams = {
			successData: [1, 2, 3],
			errorData: [4, 5, 6],
		};

		const result: Array<SerieData> = getSerie(params);

		expect(result).toEqual([
			{
				name: "Error",
				data: params.errorData,
			},
			{
				name: "Success",
				data: params.successData,
			},
		]);
	});
	it("should return empty arrays for success and error data if not provided", () => {
		const params: uiChartGetSerieParams = {
			successData: [],
			errorData: [],
		};

		const result: Array<SerieData> = getSerie(params);

		expect(result).toEqual([
			{
				name: "Error",
				data: [],
			},
			{
				name: "Success",
				data: [],
			},
		]);
	});
	it("should return series with a single empty array if either data is undefined/null", () => {
		const params: uiChartGetSerieParams = {
			successData: null,
			errorData: undefined,
		};

		const result: Array<SerieData> = getSerie(params);

		expect(result).toEqual([
			{
				name: "Error",
				data: [],
			},
			{
				name: "Success",
				data: [],
			},
		]);
	});

	// getSerieParams function
	it("should return array of values from successData and errorData sorted", () => {
		const data: BrandRatio[] = [
			{ brand_id: 1, total: 100, success: 70, error: 30 },
			{ brand_id: 2, total: 80, success: 60, error: 20 },
			{ brand_id: 3, total: 120, success: 90, error: 30 },
		];

		const result = getSerieParams(data);

		expect(result).toEqual({
			successData: [90, 70, 60],
			errorData: [30, 30, 20],
		});
	});
	it("should return empty successData and errorData arrays for empty input data", () => {
		const data: BrandRatio[] = [];

		const result = getSerieParams(data);

		expect(result).toEqual({
			successData: [],
			errorData: [],
		});
	});
	it("should return array of values from successData and errorData sorted", () => {
		const data: BrandRatio[] = [
			{ brand_id: 1, total: 100, success: undefined, error: 30 },
			{ brand_id: 2, total: 80, success: 60, error: 20 },
			{ brand_id: 3, total: 120, success: null, error: 30 },
		];

		const result = getSerieParams(data);

		expect(result).toEqual({
			successData: [60, 0, 0],
			errorData: [20, 30, 30],
		});
	});

	// sortData function
	it("should sort data based on the sum of success and error in descending order", () => {
		const data: BrandRatio[] = [
			{ brand_id: 1, total: 100, success: 70, error: 30 },
			{ brand_id: 2, total: 80, success: 60, error: 20 },
			{ brand_id: 3, total: 120, success: 90, error: 30 },
		];

		const result = sortData(data);

		expect(result).toEqual([
			{ brand_id: 3, total: 120, success: 90, error: 30 },
			{ brand_id: 1, total: 100, success: 70, error: 30 },
			{ brand_id: 2, total: 80, success: 60, error: 20 },
		]);
	});

	it("should handle null and undefined inputs as value 0", () => {
		const data: BrandRatio[] = [];

		const result = sortData(data);

		expect(result).toEqual([]);
	});

	const data: BrandRatio[] = [
		{ brand_id: 1, total: 100, success: undefined, error: 30 },
		{ brand_id: 2, total: 80, success: 60, error: 20 },
		{ brand_id: 3, total: 120, success: null, error: 30 },
	];

	const result = sortData(data);

	expect(result).toEqual([
		{ brand_id: 2, total: 80, success: 60, error: 20 },
		{ brand_id: 1, total: 100, success: 0, error: 30 },
		{ brand_id: 3, total: 120, success: 0, error: 30 },
	]);
});
