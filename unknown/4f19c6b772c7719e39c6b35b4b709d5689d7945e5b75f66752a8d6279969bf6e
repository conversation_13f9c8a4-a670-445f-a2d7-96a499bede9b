import { isRef, ref, unref } from "vue";
import { useTableQuery } from "./useTableQuery";
import { nextTick } from "vue";
import { describe, it, expect, vi } from "vitest";

vi.mock("@vueuse/router", () => ({
	useRouteQuery: vi.fn(() => ref()),
}));

describe("useTableQuery", () => {
	it("isLoading value should initially be false and tableData should initially be an empty array", () => {
		const { isLoading, tableData } = useTableQuery("endpoint", []);
		expect(isRef(isLoading)).toBe(true);
		expect(isRef(tableData)).toBe(true);
		expect(unref(isLoading)).toBe(false);
		expect(unref(tableData)).toEqual([]);
	});

	it("fetchData changes isLoading to true and then back to false", async () => {
		const { fetchData, isLoading } = useTableQuery("endpoint", []);
		expect(unref(isLoading)).toBe(false);
		fetchData();
		expect(unref(isLoading)).toBe(true);
		await fetchData(); // wait for promise to resolve
		expect(unref(isLoading)).toBe(false);
	});

	it("fetchData populates tableData correctly", async () => {
		const { fetchData, tableData } = useTableQuery("endpoint", []);
		await fetchData();
		await nextTick(); // wait for promise to resolve
		expect(unref(tableData).length).toBeGreaterThan(1);
	});
});
