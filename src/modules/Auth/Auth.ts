import { Auth } from "aws-amplify";
import { CognitoUser } from "@aws-amplify/auth";

const authErrorValues = [
	"PasswordResetRequiredException",
	"NotAuthorizedException",
	"NewPasswordRequiredException",
	"SoftwareTokenMfaException",
	"LimitExceededException",
	"CodeMismatchException",
	"UserNotFoundException",
	"DefaultException",
	"ExpiredCodeException",
] as const;

// The [number] index signature is used to create a union type that includes all the distinct types of the elements in the tuple `authErrorValues`

export type AuthError = typeof authErrorValues[number];

export async function login(email: string, password: string) {
	try {
		const user = await Auth.signIn({
			username: email,
			password: password,
		});

		if (user.challengeName === "NEW_PASSWORD_REQUIRED") {
			throw { user, code: "NewPasswordRequiredException" };
		}

		if (user.challengeName === "SOFTWARE_TOKEN_MFA") {
			throw { user, code: "SoftwareTokenMfaException" };
		}

		return formatUserData(user);
	} catch (error) {
		if (authErrorValues.includes(error.code)) {
			throw error;
		} else {
			throw { code: "DefaultException" };
		}
	}
}

export async function confirmMfa(cognitoUser: CognitoUser, code: string) {
	const user = await Auth.confirmSignIn(
		cognitoUser,
		code,
		"SOFTWARE_TOKEN_MFA",
	);

	return formatUserData(user);
}

async function formatUserData(user) {
	if (user?.attributes?.["custom:brand_id"]) {
		user.brands = user.attributes["custom:brand_id"].split(",").map(Number);
	}

	const session = await Auth.currentSession().catch(() => null);

	if (session?.getIdToken()) {
		user.roles = session.getIdToken().payload["cognito:groups"];
	}

	if (user) {
		return user;
	}
}
