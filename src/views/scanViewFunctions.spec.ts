import { describe, it, expect, expectTypeOf, vi } from "vitest";
import { beforeEach } from "vitest";
import { assertFormattedDataIsNotNull } from "@/helpers/testHelpers";
import { useScanStore } from "@/stores/scan";
import { useBrandStore } from "@/stores/brand";
import { createPinia, setActivePinia } from "pinia";
import flushPromises from "flush-promises";
import { mount } from "@vue/test-utils";
import ScansViewVue from "@/views/ScansView.vue";
import { ref } from "vue";
import messages from "@/locales/en";
import mocks from "@/../mocks/modules/Request/requestMock.json";
import { t } from "@/locales";

describe("formats data correctly", async () => {
	beforeEach(() => {
		vi.mock("@vueuse/router", () => ({
			useRouteQuery: vi.fn(() => ref()),
		}));
		vi.mock("vue-router", async () => {
			const actual = (await vi.importActual("vue-router")) as object;
			return {
				...actual,
				useRoute: vi.fn().mockReturnValue({
					params: { brand_id: "1" },
				}),
				useRouter: vi.fn(() => ref()),
			};
		});
		setActivePinia(createPinia());
		const scanStore = useScanStore();
		const brandStore = useBrandStore();

		scanStore.getScans = vi.fn();
		brandStore.$state.brands = [
			{
				brandInfo: {
					id: 1,
					time_zone: "Europe/Madrid",
				},
			},
		];
	});

	it("returns an array of formatted data and adapts header", async () => {
		const scanStore = useScanStore();
		scanStore.$state.items = null;

		const wrapper = await mount(ScansViewVue);

		scanStore.$state.items = mocks.scan.mockData1;

		await flushPromises();

		const { formattedScans, header } = wrapper.vm;

		assertFormattedDataIsNotNull(formattedScans);
		expect(formattedScans).toStrictEqual(mocks.scan.expectedResponse1);
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.errorCode"), value: "error_code" },
			{ name: t("filterTable.source.title"), value: "source" },
			{ name: t("filterTable.reservationId"), value: "" },
			{ name: t("filterTable.checkIn"), value: "" },
			{ name: t("filterTable.checkOut"), value: "" },
			{ name: t("filterTable.side"), value: "" },
			{ name: t("filterTable.firstName"), value: "" },
			{ name: t("filterTable.lastName"), value: "" },
			{ name: t("filterTable.nationality"), value: "" },
			{ name: t("filterTable.documentNumber"), value: "" },
			{ name: t("filterTable.document_type.title"), value: "" },
		]);
		formattedScans.forEach((scan) => {
			expectTypeOf(scan).toBeObject();
			expect(scan).toHaveProperty("id");
			expect(scan).toHaveProperty("row");

			expectTypeOf(scan.id).toBeNumber();
			expectTypeOf(scan.row).toBeArray();
		});
	});

	it("formats data without errors correctly and adapts header", async () => {
		const mockDataSuccess = [
			{
				time: "2023-11-16T15:30:00.000Z",
				trace_id: "7763b0d9-c99c-46c8-9e1b-d7c632b3ac1e",
				name: "scan",
				error: "false",
				source: "autocheckin",
				params: {
					check_in: "2023-07-25",
					check_out: "2023-07-30",
					country: "ES",
					allowExpiredDocuments: true,
					image: "hidden",
				},
				response: {
					valid: true,
					data: {
						document_type: "identity_card",
						document_subtype: "D",
						side: "front",
						nationality: "ESP",
						document_number: "99999999R",
						document_support_number: "CAA000000",
						surname: "Española",
						name: "Carmen",
						birthday_date: "1980-01-01T00:00:00",
						place_of_birth: null,
						gender: "female",
						date_of_issue: "2021-06-02T00:00:00",
						date_of_expiry: "2031-06-02T00:00:00",
						issuing_country: null,
						issuing_institution: null,
						residence_country: null,
						address: {
							street_name: null,
							house_number: null,
							postcode: null,
							province: null,
						},
						second_surname: "Española",
						face: "hidden",
						signature: "hidden",
					},
				},
			},
		];
		const scanStore = useScanStore();
		scanStore.$state.items = null;

		const wrapper = await mount(ScansViewVue);
		scanStore.$state.items = mockDataSuccess;

		await flushPromises();

		const { formattedScans, header } = wrapper.vm;
		expect(formattedScans[0].row[0]).toBe(mockDataSuccess[0].trace_id);
		expect(formattedScans[0].row[1]).toBe("2023-11-16 16:30:00");
		expect(formattedScans[0].row[2].content).toBe(messages.common.success);
		expect(formattedScans[0].row[3]).toBe("Autocheckin");
		expect(formattedScans[0].row[4]).toBe(mockDataSuccess[0].params.check_in);
		expect(formattedScans[0].row[5]).toBe(mockDataSuccess[0].params.check_out);
		expect(formattedScans[0].row[6]).toBe(
			mockDataSuccess[0].response.data.side,
		);
		expect(formattedScans[0].row[7]).toBe(
			mockDataSuccess[0].response.data.name,
		);
		expect(formattedScans[0].row[8]).toBe(
			mockDataSuccess[0].response.data.second_surname,
		);
		expect(formattedScans[0].row[9]).toBe(
			mockDataSuccess[0].response.data.nationality,
		);
		expect(formattedScans[0].row[10]).toBe(
			mockDataSuccess[0].response.data.document_number,
		);
		expect(formattedScans[0].row[11]).toBe(
			messages.filterTable.document_type.types.identity_card,
		);
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.source.title"), value: "source" },
			{ name: t("filterTable.checkIn"), value: "" },
			{ name: t("filterTable.checkOut"), value: "" },
			{ name: t("filterTable.side"), value: "" },
			{ name: t("filterTable.firstName"), value: "" },
			{ name: t("filterTable.lastName"), value: "" },
			{ name: t("filterTable.nationality"), value: "" },
			{ name: t("filterTable.documentNumber"), value: "" },
			{ name: t("filterTable.document_type.title"), value: "" },
		]);
	});

	it("formats data with errors correctly, show correct error Messages and adapts header", async () => {
		const scanStore = useScanStore();
		scanStore.$state.items = null;
		const newMock = [mocks.scan.mockData1[1]];

		const wrapper = await mount(ScansViewVue);
		scanStore.$state.items = newMock;

		await flushPromises();

		const { formattedScans, header } = wrapper.vm;
		const expectedErrorContent = {
			content: "Error",
			color: "danger",
			type: "tag",
		};
		const expectedErrorMessage = "Document Not Allowed";
		expect(formattedScans[0].row[2]).toEqual(expectedErrorContent);
		expect(formattedScans[0].row[3]).toBe(expectedErrorMessage);
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.errorCode"), value: "error_code" },
			{ name: t("filterTable.source.title"), value: "source" },
			{ name: t("filterTable.reservationId"), value: "" },
			{ name: t("filterTable.checkIn"), value: "" },
			{ name: t("filterTable.checkOut"), value: "" },
			{ name: t("filterTable.side"), value: "" },
			{ name: t("filterTable.firstName"), value: "" },
			{ name: t("filterTable.lastName"), value: "" },
			{ name: t("filterTable.nationality"), value: "" },
			{ name: t("filterTable.documentNumber"), value: "" },
			{ name: t("filterTable.document_type.title"), value: "" },
		]);
	});
	it("formats success data with missing optional fields correctly and adapts header", async () => {
		const mockDataSuccessMissingFields = [
			{
				time: "2023-11-16T15:30:00.000Z",
				trace_id: "aa488687-d0c4-4458-854c-daca818e2e02",
				name: "checkin",
				error: "0",
				source: "autocheckin",
				params: {
					res_id: null,
					check_in: null,
					check_out: null,
					guests: [
						{
							name: null,
							surname: null,
							second_surname: null,
							nationality: null,
						},
					],
				},
				response: "",
			},
		];
		const scanStore = useScanStore();
		scanStore.$state.items = null;

		const wrapper = await mount(ScansViewVue);
		scanStore.$state.items = mockDataSuccessMissingFields;

		await flushPromises();

		const { formattedScans, header } = wrapper.vm;
		expect(formattedScans[0].row).toEqual([
			"aa488687-d0c4-4458-854c-daca818e2e02",
			"2023-11-16 16:30:00",
			{ color: "success", content: "Success", type: "tag" },
			"Autocheckin",
		]);
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.source.title"), value: "source" },
		]);
	});
});
