export default {
	INT_1_0: "Internal server error",
	INT_1_1: "Integration exception",
	INT_1_2: "Bad Params To Mount Request",
	INT_1_3: "Action Not Implemented Exception",
	INT_1_4: "Integration Not Activated Exception",
	INT_1_5: "Invalid Request Exception",
	INT_1_6: "Bad Request Exception",
	INT_1_7: "Forbidden Request Exception",
	INT_1_8: "Event doesn't exist",
	INT_1_9: "Integration Brand Not Found Exception",
	INT_2_0: "External server error",
	INT_2_1: "Request Exception",
	INT_2_2: "Client Exception",
	INT_2_3: "PMS Error",
	INT_2_4: "Time Out",
	INT_2_5: "Too Many Attempts",
	INT_2_6: "PMS Failed Exception",
	INT_2_7: "PMS Request Failed",
	INT_2_8: "Response Not Valid",
	INT_3_0: "Logic server error",
	INT_3_1: "Reservation doesn't exist",
	INT_3_2: "Reservation Is In Precheckin State",
	INT_3_4: "Portal Pro Exception",
	INT_3_5: "No Data Exception",
	INT_3_6: "No Room Request Exception",
	INT_3_7: "No User Match Exception",
	INT_3_8: "Invalid event",
	OCR_1_1: "Unknown",
	OCR_1_2: "Document scanning request doesn't have the necessary parameters",
	OCR_3_2: "Document Not Allowed",
	OCR_3_3: "Driving License",
	OCR_3_4: "Document Expired",
	OCR_3_6: "No minimum data scanned",
	OCR_3_7: "Scanned image isn't an allowed document",
	OCR_3_8: "Service Not Available",
	ACI_1_1: "Server error",
	ACI_3_1: "Reservation Not Found",
	ACI_3_2: "Invalid reservation",
};
