import { describe, it, expect } from "vitest";
import { formatData, formatDataExtended, filterEmptyFields } from "./uiTable";

describe("formatData function behaviour", () => {
	it("should format time properly according to brandTimeZone, handling undefined and '' values", () => {
		const time = "2023-11-16T15:30:00.000Z";
		const brandTimeZone = "Europe/Madrid";

		const result = formatData(time, "", "", brandTimeZone);
		expect(result.formattedTime).toBe("2023-11-16 16:30:00");

		const undefinedTime = undefined;
		const result2 = formatData(undefinedTime, "", "", brandTimeZone);
		expect(result2.formattedTime).toBe("-");

		const undefinedBrandTimeZone = undefined;
		const result3 = formatData(time, "", "", undefinedBrandTimeZone);
		expect(result3.formattedTime).toBe("-");

		const result4 = formatData("", "", "", "");
		expect(result4.formattedTime).toBe("-");
	});

	it("should format errorTag properly", () => {
		const error1 = "0";
		const error2 = "1";

		const result1 = formatData("", error1, "", "");
		const result2 = formatData("", error2, "", "");

		expect(result1.errorTag).toStrictEqual({
			content: "Success",
			color: "success",
			type: "tag",
		});
		expect(result2.errorTag).toStrictEqual({
			content: "Error",
			color: "danger",
			type: "tag",
		});
	});

	it("should format errorMessage properly", () => {
		const response1 = "";
		const response2 = {
			error: {
				code: "INT_2_8",
			},
		};
		const response3 = {
			error: {
				code: "INT_2_8",
				message: "The message from PMS",
			},
		};

		const result1 = formatData("", "1", response1, "");
		const result2 = formatData("", "1", response2, "");
		const result3 = formatData("", "1", response3, "");

		expect(result1.errorMessage).toBe("-");
		expect(result2.errorMessage).toBe("Response Not Valid");
		expect(result3.errorMessage).toStrictEqual({
			content: "Response Not Valid",
			type: "link",
			emits: "showMessageModal",
			message: "The message from PMS",
		});
	});

	it("should format source properly", () => {
		const source1 = "reception";
		const source2 = "autocheckin";
		const source3 = "wrongSource";

		const result1 = formatDataExtended("", "", source1, "", "");
		const result2 = formatDataExtended("", "", source2, "", "");
		const result3 = formatDataExtended("", "", source3, "", "");

		expect(result1.sourceTranslation).toBe("Reception");
		expect(result2.sourceTranslation).toBe("Autocheckin");
		expect(result3.sourceTranslation).toBe("");
	});
});

describe("filterEmptyFields function behaviour", () => {
	it("should return the same data when no indices are provided", () => {
		const data = ["A", "B", "C"];
		const result = filterEmptyFields(data, []);
		expect(result).toEqual(data);
	});

	it("should remove elements at the provided indices", () => {
		const data = ["A", "B", "C", "D"];
		const result = filterEmptyFields(data, [1, 3]);
		expect(result).toEqual(["A", "C"]);
	});

	it("should return an empty array if all indices are removed", () => {
		const data = ["A", "B"];
		const result = filterEmptyFields(data, [0, 1]);
		expect(result).toEqual([]);
	});
});
