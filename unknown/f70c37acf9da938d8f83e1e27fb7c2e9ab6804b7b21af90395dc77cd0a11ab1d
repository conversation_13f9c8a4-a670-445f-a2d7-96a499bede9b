import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { calculateDaysBefore, validateDateRange } from "@/helpers/dates";

describe("calculateDaysBefore function", () => {
	it("should return correct values based on the range", () => {
		const range1 = "24h";
		const result1 = calculateDaysBefore(range1);
		const range2 = "7d";
		const result2 = calculateDaysBefore(range2);
		const range3 = "1m";
		const result3 = calculateDaysBefore(range3);
		const range4 = "3m";
		const result4 = calculateDaysBefore(range4);
		const range5 = undefined;
		const result5 = calculateDaysBefore(range5);
		const range6 = { from: "2024-04-10", to: "2024-04-15" };
		const result6 = calculateDaysBefore(range6);
		expect(result1).toEqual({ days_before: 1, interval: "1h" });
		expect(result2).toEqual({ days_before: 7, interval: "1d" });
		expect(result3).toEqual({ days_before: 30, interval: "1d" });
		expect(result4).toEqual({ days_before: 90, interval: "1d" });
		expect(result5).toEqual({ days_before: 1, interval: "1h" });
		expect(result6).toEqual({ days_before: 5, interval: "1d" });
	});
});

describe("validateDateRange function", () => {
	beforeEach(() => {
		vi.useFakeTimers();
		const date = new Date("2024-04-16");
		vi.setSystemTime(date);
	});

	afterEach(() => {
		vi.useRealTimers();
	});

	it("should return false if date from is after date to", () => {
		const range = { from: "2024-04-11", to: "2024-04-10" };
		const result = validateDateRange(range);
		expect(result).toEqual(false);
	});

	it("should return false if any date is in future", () => {
		const range = { from: "2024-04-12", to: "2024-04-18" };
		const result = validateDateRange(range);
		expect(result).toEqual(false);
	});

	it("return true if dates are correct", () => {
		const range = { from: "2024-04-11", to: "2024-04-15" };
		const result = validateDateRange(range);
		expect(result).toEqual(true);
	});
});
