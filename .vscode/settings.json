{
  "editor.defaultFormatter": "rome.rome",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports.rome": "explicit",
    "quickfix.rome": "explicit",
    "source.fixAll.esLint": "explicit"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    
  },
  "[json]": {
    "editor.insertSpaces": true,
    "editor.tabSize": 2,
    "editor.autoIndent": "advanced",
    "editor.formatOnSave": true,
    "editor.trimAutoWhitespace": true
  },
  "editor.insertSpaces": true,
  "editor.tabSize": 2,
  "prettier.semi": true,
  "prettier.singleQuote": false,
  "prettier.tabWidth": 2,
  "prettier.useTabs": false,
  "prettier.trailingComma": "es5"
}
