import { useUserStore } from "@/stores/user";
import { ref, watch } from "vue";
import type { Ref, FunctionalComponent, HTMLAttributes, VNodeProps } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useBrandStore } from "../stores/brand";
import { useComparisonStore } from "@/stores/comparison";

interface SidebarElement {
	name: string;
	icon?: string | FunctionalComponent<HTMLAttributes & VNodeProps, {}>;
	current?: boolean;
	id?: string;
	color?: string;
	routeName?: string;
}

export const useSidebar = (sideBarItems) => {
	const router = useRouter();
	const route = useRoute();
	const userStore = useUserStore();
	const brandStore = useBrandStore();
	const comparisonStore = useComparisonStore();

	const sidebarElements: Array<SidebarElement> = sideBarItems;

	const sidebar: Ref<Array<SidebarElement>> = ref(sidebarElements);

	// we watch the change in routing, if the route name is the same as the sidebar item id then we update the current attribute to true
	watch(
		() => ({
			name: route.name,
		}),
		(route) => {
			sidebar.value = sidebar.value.map((item) => {
				if (item.id === route.name) {
					item.current = true;
				} else {
					item.current = false;
				}
				return item;
			});
		},
		{
			immediate: true,
		},
	);

	const cleanQueryParamsAndReload = async () => {
		await router.replace({
			path: router.currentRoute.value.path,
			replace: true,
		});
	};

	const sidebarActions = async (routeName: string, brand_id?: string) => {
		switch (routeName) {
			case "logout":
				await userStore.logout();
				await brandStore.reset();
				await comparisonStore.reset();
				router.push({ name: "login" });
				break;
			case "userManagement":
				if (router.currentRoute.value.name === "userManagement") {
					await cleanQueryParamsAndReload();
				}
				router.push({ name: "userManagement" });
				break;
			case "logo":
				if (router.currentRoute.value.name === "brands") {
					await cleanQueryParamsAndReload();
				}
				router.push({ name: "accounts" });
				break;
			default:
				//Clean queryParams before clicking on current Page option for breadCrumb/sidebar option
				if (router.currentRoute.value.name === routeName) {
					await cleanQueryParamsAndReload();
				}
				console.log("Routename", routeName);
				//try {
				router.push({ name: routeName, params: { brand_id } });
				//} catch (error) {
				//console.log("The error!", error);
				//}
				break;
		}
	};

	return { sidebar, sidebarActions };
};
