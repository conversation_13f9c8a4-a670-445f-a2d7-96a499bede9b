export default {
	title: "Create New User",
	usersCreateTitle: "Fill out the following form to create a user",
	usersCreateText:
		"Enter the user's email, the IDs of the establishments they will manage, and their management level within the establishments.",
	emailPlaceholder: "Enter an email address...",
	emailLabel: "User's email",
	brandsLabel: "Brands IDs",
	brandsPlaceholder: "Separated by commas (1,2,3...)",
	roles: {
		admin: "admin",
		brandAdmin: "brand_admin",
		reception: "reception",
	},
	roleLabel: "Group",
	successUserCreateTitle: "User created successfully",
	errorUserCreateTitle: "Error creating user",
	errorUserCreateMessage:
		"An error occurred while trying to create the user. Please try again later or contact our Customer Success team.",
	userData: "Here are the details of the new user you have created:",
	userEmail: "User's Email:",
	storedPassword: "Generated Password:",
	brandIds: "Brand IDs:",
	role: "Assigned Role:",
	breadCrumb: {
		selector: "Create new user",
	},
	success: {
		title: "New user created",
		body: "The user has been created successfully.",
	},
	brandsError: "You must enter at least one establishment ID",
	groupsError: "You must select at least one group",
	mfaLabel: "Enable MFA",
};
