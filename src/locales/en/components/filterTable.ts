export default {
	filterBy: "Filter by",
	createButton: "Create user",
	filterButton: "Filter",
	exportButton: "Export Results",
	sessionId: "Session ID",
	date: "Date",
	result: "Result",
	errorCode: "Error code",
	source: {
		title: "Source",
		types: {
			autocheckin: "Autocheckin",
			reception: "Reception",
		},
	},
	roomNumber: "Room number",
	reservationCode: "Localizer",
	reservationId: "Reservation ID",
	userId: "User ID",
	firstName: "First name",
	lastName: "Last name",
	checkIn: "Check in",
	checkOut: "Check out",
	side: "Side",
	nationality: "Nationality",
	documentNumber: "Document Number",
	document_type: {
		title: "Document Type",
		types: {
			identity_card: "Identity card",
			passport: "Passport",
			driving_license: "Driving license",
			residence_permit: "Residency permit",
		},
	},
	showing: "Showing",
	of: "of",
	next: "Next",
	previous: "Previous",
	noResults: {
		title: "No results found",
		body: "Please try one of the following actions",
		back: "Back",
		home: "Return to home",
	},
	delete: "delete",
	manual: "Manual",
	brandIds: "Brand IDs",
	group: "Group",
	tableHeaderCreatedAt: "Created at",
	tableHeaderUpdatedAt: "Updated at",
	mfa: "MFA",
	breadCrumb: {
		selector: "Users management",
	},
};
