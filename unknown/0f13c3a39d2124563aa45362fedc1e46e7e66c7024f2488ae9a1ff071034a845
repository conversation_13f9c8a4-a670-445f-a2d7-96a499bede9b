{"precheckin": {"mockData1": [{"time": "2023-11-16T15:30:00.000Z", "trace_id": "aa488687-d0c4-4458-854c-daca818e2e02", "name": "checkin", "error": "0", "source": "autocheckin", "params": {"res_id": "146117-001", "check_in": "2023-08-23", "check_out": "2023-08-28", "guests": [{"name": "vgfdg", "surname": "fdgfg", "second_surname": null, "nationality": "AFG"}]}, "response": ""}, {"time": "2009-04-22T10:00:00.000Z", "trace_id": "5cdf70be-b7de-4c91-b03e-738f197c6eaf", "name": "checkin", "error": "1", "error_code": "INT_3_2", "source": "reception", "params": {"res_id": "145304-002", "check_in": "2022-01-26", "check_out": "2022-01-27", "guests": [{"name": "In**", "surname": "Mu********", "second_surname": "Te***", "nationality": "DEU"}]}, "response": {"error": {"code": "INT_3_2"}}}], "expectedResponse1": [{"id": 1, "row": ["aa488687-d0c4-4458-854c-daca818e2e02", "2023-11-16 16:30:00", {"color": "success", "content": "Success", "type": "tag"}, "-", "Autocheckin", "146117-001", "2023-08-23", "2023-08-28", "vgfdg", "fdgfg", "AFG"]}, {"id": 2, "row": ["5cdf70be-b7de-4c91-b03e-738f197c6eaf", "2009-04-22 12:00:00", {"color": "danger", "content": "Error", "type": "tag"}, "Reservation Is In Precheckin State", "Reception", "145304-002", "2022-01-26", "2022-01-27", "In**", "Mu********", "DEU"]}]}, "scan": {"mockData1": [{"time": "2023-11-16T15:30:00.000Z", "trace_id": "7763b0d9-c99c-46c8-9e1b-d7c632b3ac1e", "name": "scan", "error": "false", "source": "autocheckin", "params": {"check_in": "2023-07-25", "check_out": "2023-07-30", "country": "ES", "allowExpiredDocuments": true, "image": "hidden"}, "response": {"valid": true, "data": {"document_type": "identity_card", "document_subtype": "D", "side": "front", "nationality": "ESP", "document_number": "99999999R", "document_support_number": "CAA000000", "surname": "<PERSON>spa<PERSON><PERSON>", "name": "Carmen", "birthday_date": "1980-01-01T00:00:00", "place_of_birth": null, "gender": "female", "date_of_issue": "2021-06-02T00:00:00", "date_of_expiry": "2031-06-02T00:00:00", "issuing_country": null, "issuing_institution": null, "residence_country": null, "address": {"street_name": null, "house_number": null, "postcode": null, "province": null}, "second_surname": "<PERSON>spa<PERSON><PERSON>", "face": "hidden", "signature": "hidden"}}}, {"time": "2013-05-29T10:00:00.000Z", "trace_id": "0625a652-b55b-4ea5-9787-f56b4bc71534", "name": "scan", "error": "1", "error_code": "OCR_3_2", "source": "autocheckin", "params": {"country": "MX", "images": ["hidden"], "allowExpiredDocuments": true, "allowDrivingLicense": true, "res_id": "146267-001", "check_in": "2023-11-30", "check_out": "2023-12-05"}, "response": {"error": {"code": "OCR_3_2", "data": {"document_type": "identity_card", "document_subtype": "D", "side": "front", "nationality": "ESP", "document_number": "99*******", "document_support_number": "CA*******", "surname": "Es******", "name": "Ca****", "birthday_date": "19*****************", "place_of_birth": null, "gender": "female", "date_of_issue": "2021-06-02T00:00:00", "date_of_expiry": "2031-06-02T00:00:00", "issuing_country": null, "issuing_institution": null, "residence_country": null, "address": {"street_name": null, "house_number": null, "postcode": null, "province": null}, "signature": "hidden", "face": "hidden", "second_surname": "Es******"}}}}], "expectedResponse1": [{"id": 1, "row": ["7763b0d9-c99c-46c8-9e1b-d7c632b3ac1e", "2023-11-16 16:30:00", {"color": "success", "content": "Success", "type": "tag"}, "-", "Autocheckin", "-", "2023-07-25", "2023-07-30", "front", "Carmen", "<PERSON>spa<PERSON><PERSON>", "ESP", "99999999R", "Identity card"]}, {"id": 2, "row": ["0625a652-b55b-4ea5-9787-f56b4bc71534", "2013-05-29 12:00:00", {"color": "danger", "content": "Error", "type": "tag"}, "Document Not Allowed", "Autocheckin", "146267-001", "2023-11-30", "2023-12-05", "front", "Ca****", "Es******", "ESP", "99*******", "Identity card"]}]}}