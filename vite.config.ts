import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "node:path";
import { nodePolyfills } from "vite-plugin-node-polyfills";

// https://vitejs.dev/config/
export default defineConfig({
	plugins: [
		vue(),
		nodePolyfills({
			// To exclude specific polyfills, add them to this list.
			exclude: [
				"fs", // Excludes the polyfill for `fs` and `node:fs`.
			],
			// Whether to polyfill specific globals.
			globals: {
				Buffer: true, // can also be 'build', 'dev', or false
				global: true,
				process: true,
			},
			// Whether to polyfill `node:` protocol imports.
			protocolImports: true,
		}),
	],
	resolve: {
		extensions: [".js", ".ts", ".vue"],
		alias: {
			"@": resolve(__dirname, "./src"),
			"@mocks": resolve(__dirname, "./mocks"),
		},
	},
	// test: {
	// 	root: "./tests/unit",
	// 	globals: true,
	// 	environment: "happy-dom",
	// },
});
