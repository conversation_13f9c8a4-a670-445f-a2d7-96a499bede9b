import { describe, it, vi, afterEach, expect } from "vitest";
import {
	getComparisonRatioStats,
	getRatioStats,
} from "@/modules/Comparison/Comparison";
import comparisonMock from "@mocks/modules/Comparison/comparisonMock.json";
import { API } from "@aws-amplify/api";

vi.mock("@aws-amplify/api", () => ({
	API: {
		get: vi.fn(),
	},
}));

describe("Comparison modules", () => {
	afterEach(() => {
		vi.clearAllMocks();
	});
	it("should return and save correctly data", async () => {
		API.get.mockResolvedValue({data: comparisonMock});
		const params = {
			brandIds: [2, 76, 83],
		};
		const response = await getComparisonRatioStats(params);
		expect(response).toEqual(comparisonMock);
		expect(response.current.reservation).toEqual(comparisonMock.current.reservation);
		expect(response.current.scan).toEqual(comparisonMock.current.scan);
		expect(response.current.checkin).toEqual(comparisonMock.current.checkin);
	});
	it("should save as empty array if does not get type response", async () => {
		const mock = {
			data: {
				current:{
					scan: [
						{ brand_id: 76, total: 20, success: 0, error: 20 },
						{ brand_id: 2, total: 0, success: 0, error: 0 },
						{ brand_id: 83, total: 3, success: 2, error: 1 },
					],
				},
				previous:{
					scan: [
						{ brand_id: 76, total: 20, success: 10, error: 10 },
						{ brand_id: 2, total: 2, success: 2, error: 0 },
						{ brand_id: 83, total: 6, success: 2, error: 4 },
					],
				}
			
			},
		};
		const expectedResponse = {
			current: {
				...mock.data.current,
				reservation: [],
				checkin: [],
			},
			previous: {
				...mock.data.previous,
				reservation: [],
				checkin: [],
			},
		};

		API.get.mockResolvedValue(mock);
		const params = {
			brandIds: [2, 76, 83],
		};
		const response = await getComparisonRatioStats(params);
		expect(response).toEqual(expectedResponse);
		expect(response.current.reservation).toEqual([]);
		expect(response.current.scan).toEqual(expectedResponse.current.scan);
		expect(response.current.checkin).toEqual([]);

		expect(response.previous.reservation).toEqual([]);
		expect(response.previous.scan).toEqual(expectedResponse.previous.scan);
		expect(response.previous.checkin).toEqual([]);
	});
	it("should throw error when no brandIds were given", async () => {
		try {
			await getRatioStats([], "aslkdm", "2023-10-17");
		} catch (error) {
			expect(error.message).toBe(
				"getBrandsRatio request failed: => Error: no brands were selected",
			);
		}
	});
	it("should throw error when API call fails", async () => {
		API.get.mockRejectedValue(new Error("API call failed")); // Mock an error response
		try {
			await getRatioStats([], "test", "2023-10-17");
		} catch (error) {
			expect(error).toBeDefined();
		}
	});
	it("should handle range with from and to dates correctly", async () => {
		const mock = {
			data: {
				current: {
					scan: [
						{ brand_id: 76, total: 20, success: 0, error: 20 },
						{ brand_id: 2, total: 0, success: 0, error: 0 },
						{ brand_id: 83, total: 3, success: 2, error: 1 },
					],
				},
				previous: {
					scan: [
						{ brand_id: 76, total: 20, success: 10, error: 10 },
						{ brand_id: 2, total: 2, success: 2, error: 0 },
						{ brand_id: 83, total: 6, success: 2, error: 4 },
					],
				},
			},
		};
		
		const rangeWithDates = {
			from: "2024-04-01",
			to: "2024-04-30",
		};
	
		const expectedResponse = {
			current: {
				...mock.data.current,
				reservation: [],
				checkin: [],
			},
			previous: {
				...mock.data.previous,
				reservation: [],
				checkin: [],
			},
		};
	
		// Mock API response
		API.get.mockResolvedValue(mock);
	
		const params = {
			brandIds: [2, 76, 83],
			range: rangeWithDates,  // Passing the range with from and to dates
		};
	
		const response = await getComparisonRatioStats(params);
		
		// Verifying that the response is correctly processed
		expect(response).toEqual(expectedResponse);
		expect(response.current.reservation).toEqual([]);
		expect(response.current.scan).toEqual(expectedResponse.current.scan);
		expect(response.current.checkin).toEqual([]);
	
		expect(response.previous.reservation).toEqual([]);
		expect(response.previous.scan).toEqual(expectedResponse.previous.scan);
		expect(response.previous.checkin).toEqual([]);
	});
});
