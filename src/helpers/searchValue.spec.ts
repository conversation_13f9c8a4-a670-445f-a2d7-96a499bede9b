import { describe, it, expect } from "vitest";
import { transformErrorValue, getErrorCode } from "./searchValue";
import { i18n } from "@/locales";

describe("transformErrorValue function", () => {
	it("it should return the correct value based on the searchValue, being case-insensitive", () => {
		i18n.global.locale.value = "en";
		const result1 = transformErrorValue("SUCCESS");
		const result2 = transformErrorValue("success");
		const result3 = transformErrorValue("SucCesS");
		const result4 = transformErrorValue("eRRor");
		const result5 = transformErrorValue("ANYTHING else");

		i18n.global.locale.value = "es";
		const result6 = transformErrorValue("éxito");
		const result7 = transformErrorValue("EXITO");
		const result8 = transformErrorValue("eRRor");
		const result9 = transformErrorValue("cualquier OTRO");

		expect(result1).toEqual("0");
		expect(result2).toEqual("0");
		expect(result3).toEqual("0");
		expect(result4).toEqual("1");
		expect(result5).toEqual("anything else");
		expect(result6).toEqual("0");
		expect(result7).toEqual("0");
		expect(result8).toEqual("1");
		expect(result9).toEqual("cualquier otro");
	});
});

describe("getErrorCode function", () => {
	it("it should return the correct error code based on the current locale and message received as parameter", () => {
		i18n.global.locale.value = "es";
		const result1 = getErrorCode("Fallo de PMS");
		const result2 = getErrorCode("Reserva inexistente");
		const result3 = getErrorCode("Cualquier otro que no exista");

		i18n.global.locale.value = "en";
		const result4 = getErrorCode("Too Many Attempts");
		const result5 = getErrorCode("Any other error message");

		expect(result1).toEqual("INT_2_6");
		expect(result2).toEqual("INT_3_1");
		expect(result3).toEqual("Cualquier otro que no exista");
		expect(result4).toEqual("INT_2_5");
		expect(result5).toEqual("Any other error message");
	});
});
