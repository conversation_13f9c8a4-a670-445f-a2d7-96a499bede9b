import { Auth } from "aws-amplify";
import { describe, it, expect, afterEach, vi } from "vitest";
import { login } from "@/modules/Auth/Auth"; 

vi.mock("aws-amplify", () => ({
	Auth: {
		signIn: vi.fn(),
		currentSession: vi.fn(),
    // currentSession: vi.fn().mockResolvedValue({
		// 	getIdToken: () => ({
		// 		payload: { "cognito:groups": ["brand_admin"] },
		// 	}),
		// }),
	},
}));

describe("Auth module", () => {

	afterEach(() => {
		vi.clearAllMocks();
	});

	it("login function should return user with brands and roles properties if available", async () => {
		const user = {
			username: "testuser",
			attributes: { "custom:brand_id": "1,2,3" },
		};

		const userData = { email: "<EMAIL>", password: "password" };

		Auth.signIn.mockResolvedValue(user);
		Auth.currentSession.mockResolvedValue({
			getIdToken: () => ({
				payload: { "cognito:groups": ["brand_admin"] },
			}),
		})

    const result = await login(userData.email, userData.password);

		expect(Auth.signIn).toHaveBeenCalledWith({
      username: userData.email,
      password: userData.password,
    });

		expect(result).toHaveProperty("brands");
  	expect(result).toHaveProperty("roles");
  	expect(result.brands).toEqual([1, 2, 3]);
  	expect(result.roles).toEqual(["brand_admin"]);
	});

	it("login function should return user without brands and roles properties if not available", async () => {
		const user = {
			username: "user",
		};

		const userData = { email: "<EMAIL>", password: "password" };

		Auth.signIn.mockResolvedValue(user);
		Auth.currentSession.mockRejectedValue(new Error ("no session"))

    const result = await login(userData.email, userData.password);

		expect(Auth.signIn).toHaveBeenCalledWith({
      username: userData.email,
      password: userData.password,
    });

		expect(result).not.toHaveProperty("brands");
  	expect(result).not.toHaveProperty("roles");
	});
});