import { sanitizeString } from "./strings";
import { i18n } from "@/locales";

export const transformErrorValue = (searchValue: string) => {
	let updatedValue = sanitizeString(searchValue.toLowerCase());
	const success = sanitizeString(
		i18n.global.messages.value[
			i18n.global.locale.value
		].common.success.toLowerCase(),
	);
	const error = sanitizeString(
		i18n.global.messages.value[
			i18n.global.locale.value
		].common.error.toLowerCase(),
	);

	if (updatedValue === error) {
		updatedValue = "1";
	} else if (updatedValue === success) {
		updatedValue = "0";
	}

	return updatedValue;
};

export const transformSourceValue = (searchValue: string) => {
	const messages =
		i18n.global.messages.value[i18n.global.locale.value].filterTable.source
			.types;

	const sanitizedSearchValue = sanitizeString(searchValue.toLowerCase());

	const translations = {
		[sanitizeString(messages.autocheckin).toLowerCase()]: "autocheckin",
		[sanitizeString(messages.reception).toLowerCase()]: "reception",
	};

	const updatedValue =
		translations[sanitizedSearchValue] || sanitizedSearchValue;

	return updatedValue;
};

export const transformDocumentTypeValue = (searchValue: string) => {
	const sanitizedSearchValue = sanitizeString(searchValue.toLowerCase());

	const messages =
		i18n.global.messages.value[i18n.global.locale.value].filterTable
			.document_type.types;

	const identityCard = sanitizeString(messages.identity_card.toLowerCase());
	const passport = sanitizeString(messages.passport.toLowerCase());
	const drivingLicense = sanitizeString(messages.driving_license.toLowerCase());
	const residencePermit = sanitizeString(
		messages.residence_permit.toLowerCase(),
	);

	const translations = {
		[drivingLicense]: "driving_license",
		[identityCard]: "identity_card",
		[passport]: "passport",
		[residencePermit]: "residence_permit",
	};

	const updatedValue =
		translations[sanitizedSearchValue] || sanitizedSearchValue;

	return updatedValue;
};

export const getErrorCode = (message: string) => {
	const sanitizedMessage = sanitizeString(message).toLowerCase();

	const messages =
		i18n.global.messages.value[i18n.global.locale.value].errorMessages;

	if (messages) {
		for (const errorCode in messages) {
			const sanitizedLocaleMessage = sanitizeString(
				messages[errorCode],
			).toLowerCase();

			if (sanitizedLocaleMessage === sanitizedMessage) {
				return errorCode;
			}
		}

		return message;
	}
};

export const transformManualValue = (searchValue: string) => {
	const sanitizedSearchValue = sanitizeString(searchValue.toLowerCase());
	const yesString =
		i18n.global.messages.value[
			i18n.global.locale.value
		].common.yes.toLowerCase();

	const noString =
		i18n.global.messages.value[
			i18n.global.locale.value
		].common.no.toLowerCase();

	if (yesString === sanitizedSearchValue) {
		return "true";
	} else if (noString === sanitizedSearchValue) {
		return "false";
	}
};
