import { faker } from "@faker-js/faker";
import { format } from "date-fns";
import type {
	ReservationData,
	ApiReservationsResponse,
	ReservationParams,
} from "@/types";

export const errorCodes: string[] = [
	"INT_1_0",
	"INT_1_1",
	"INT_1_2",
	"INT_1_3",
	"INT_1_4",
	"INT_1_5",
	"INT_1_6",
	"INT_1_7",
	"INT_1_8",
	"INT_1_9",
	"INT_2_0",
	"INT_2_1",
	"INT_2_2",
	"INT_2_3",
	"INT_2_4",
	"INT_2_5",
	"INT_2_6",
	"INT_2_7",
	"INT_2_8",
	"INT_3_0",
	"INT_3_1",
	"INT_3_2",
	"INT_3_3",
	"INT_3_4",
	"INT_3_5",
	"INT_3_6",
	"INT_3_7",
	"INT_3_8",
	"OCR_3_2",
	"OCR_3_3",
	"OCR_3_4",
	"OCR_3_5",
	"OCR_3_6",
	"OCR_3_7",
	"OCR_3_8",
	"OCR_3_9",
	"ACI_1_1",
	"ACI_3_1",
];

function generateSearchParams(): ReservationParams {
	const params: ReservationParams = {};

	const possibleParams = [
		"reservation_code",
		"first_name",
		"last_name",
		"check_in",
		"check_out",
		"email",
	];
	const numberOfParams = faker.number.int({
		min: 1,
		max: possibleParams.length,
	});

	for (let j = 0; j < numberOfParams; j++) {
		const randomParamIndex = faker.number.int({
			min: 0,
			max: possibleParams.length - 1,
		});
		const paramName = possibleParams[randomParamIndex];

		switch (paramName) {
			case "reservation_code":
				params[paramName] = faker.string.alphanumeric(5);
				break;
			case "first_name":
				params[paramName] = faker.person.firstName();
				break;
			case "last_name":
				params[paramName] = faker.person.lastName();
				break;
			case "check_in":
				params[paramName] = format(faker.date.past(), "yyyy/MM/dd");
				break;
			case "check_out":
				params[paramName] = format(faker.date.future(), "yyyy/MM/dd");
				break;
			case "email":
				params[paramName] = faker.internet.email();
				break;
			default:
				break;
		}
		possibleParams.splice(randomParamIndex, 1);
	}
	return params;
}

function generateReservation(): ReservationData {
	const reservation: ReservationData = {
		trace_id: faker.string.alphanumeric(16),
		time: format(faker.date.anytime(), "yyyy/MM/dd"),
		error: faker.datatype.boolean().toString(),
		error_code: null,
		params: generateSearchParams(),
	};
	if (reservation.error)
		reservation.error_code = faker.helpers.arrayElement<string>(errorCodes);

	return reservation;
}

export function getReservationsMock(): ApiReservationsResponse {
	return {
		data: Array.from({
			length: faker.number.int({ min: 1, max: 30 }),
		}).map(() => generateReservation()),
		meta: {
			current_page: 6, //same as "page" selected in the frontend. It's shown in "Mostrando 6 de xx "
			from: 1, //first element shown in the view. For example, if it's shown the page 2 with 10 elementes per page, "from" will be "11"
			last_page: 15, //last page, depending on the number of reservations and the "per_page". It's shown in "Mostrando xx de 15"
			path: "http://example.com/pagination",
			per_page: 50, //number of elements shown per page. Same as "per_page" selected in the frontend. It's shown in the uiDropdown
			to: 10, //last element shown in the view. For example, if it's shown the page 2 with 10 elementes per page, "from" will be "20"
			total: 10, //total of reservations founded
		},
	};
}
