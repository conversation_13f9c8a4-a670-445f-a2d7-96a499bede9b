import { useRouter, useRoute } from "vue-router";
import { useBrandStore } from "@/stores/brand";
import { storeToRefs } from "pinia";
import type { RouteRecordName } from "vue-router";
import { ref, watch } from "vue";
import type { Ref } from "vue";
import { t } from "@/locales";
const brandStore = useBrandStore();
const { getAccountById, getBrandById } = storeToRefs(brandStore);

type Breadcrumb = {
	name: string;
	current?: boolean;
	routeName: RouteRecordName | null | undefined;
};

export const useBreadcrumbs = () => {
	const router = useRouter();
	const route = useRoute();

	//the Ref from vue of an array of Breadcrumb type Or Undefined since the init is empty
	const breadcrumbs: Ref<Array<Breadcrumb> | undefined> = ref();

	const getBreadcrumbs = (
		currRoute: RouteRecordName | null | undefined,
	): Breadcrumb[] => {
		const routes = [];
		let currentRoute = route;
		while (currentRoute) {
			routes.unshift(currentRoute); // Add to the beginning of the array to maintain order
			currentRoute = currentRoute.meta.parent
				? router.resolve({ name: currentRoute.meta.parent })
				: null;
		}

		return routes.map((route) => {
			let breadcrumbName = t(
				`sidebar.${route?.meta?.breadcrumb?.toLowerCase()}`,
			);

			if (route.name === "brands") {
				const account = getAccountById.value(Number(route.params.account_id));
				breadcrumbName = account.name;
			}

			if (route.name === "brand") {
				const brand = getBrandById.value(Number(route.params.brand_id));
				breadcrumbName = brand.name;
			}

			if (route.name === "userManagement") {
				breadcrumbName = t("filterTable.breadCrumb.selector");
			}

			if (route.name === "createUser") {
				breadcrumbName = t("userCreateView.breadCrumb.selector");
			}

			return {
				current: route.name === currRoute,
				name: breadcrumbName,
				routeName: route.name,
			};
		});
	};

	watch(
		() => ({
			name: route.name,
			meta: route.meta,
		}),
		(route) => {
			breadcrumbs.value = getBreadcrumbs(route.name);
		},
		{
			immediate: true,
		},
	);

	return {
		breadcrumbs,
	};
};
