export default {
	header: "Identify yourself to access",
	logInButton: "Access",
	forgotPassword: "Forgot your password?",
	resetPasswordButton: "reset it",
	back: "back",
	sendCodeButton: "Send code",
	requireVerification: "Additional verification required",
	identificationError: {
		title: "Identification Error",
		body: "You have entered incorrect credentials, please try again or contact our Customer Success team.",
	},
	defaultError: {
		title: "An unknown error occured.",
		body: "Please contact support.",
	},
	userNotFoundError: {
		title: "User not found.",
		body: "User does not exist, please try a different one.",
	},
	resetPassword: {
		header: "Reset your password",
		body: "Enter your email address. If there is a password associated with this email, we will send you an email with instructions to reset it.",
		resetButton: "Reset my password",
	},
	newPassword: {
		header: "New password required",
		body: "Enter your new password",
		resetButton: "Change my password",
	},
	emailInputError: "The field must contain a valid email address",
	passwordInputError: "The field cannot be empty",
	isAuth: {
		title: "Authorized user",
		body: "The user has been authorized correctly.",
	},
};
