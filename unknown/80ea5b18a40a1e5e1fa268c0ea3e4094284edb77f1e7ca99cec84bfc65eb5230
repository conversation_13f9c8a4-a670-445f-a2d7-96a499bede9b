import { chain, range } from "lodash-es";
import { format, sub, addMinutes } from "date-fns";

const createTimeSeries = (daysAgo: number, interval: number) =>
	chain(range(0, daysAgo))
		.reverse()
		.map((i) => {
			const date = sub(new Date(), { days: i + 1 });
			return range(0, 24 * 60, interval).map((minute) =>
				addMinutes(date, minute),
			);
		})
		.flatten()
		.map((date) => format(date, "yyyy-MM-dd HH:mm:ss"))
		.value();

const createRandomCounts = (daysAgo: number, interval: number) => {
	const timeSeries = createTimeSeries(daysAgo, interval);
	return timeSeries.map((t) => {
		const success = Math.floor(Math.random() * 31) + 70;
		const errors = Math.floor(Math.random() * 31);
		return {
			time: t,
			count: {
				total: success + errors,
				errors,
				success,
			},
		};
	});
};

export const getStats = async () => {
	return new Promise((resolve) => {
		setTimeout(() => {
			return resolve({
				data: {
					reservations: createRandomCounts(1, 60),
					scans: createRandomCounts(1, 60),
					precheckins: createRandomCounts(1, 60),
				},
			});
		}, 1000);
	});
};
