import reservationsView from "./ReservationsView.vue";
import { describe, it, vi, expect, expectTypeOf } from "vitest";
import { mount } from "@vue/test-utils";
import flushPromises from "flush-promises";
import {
	getReservationsMock,
	errorCodes,
} from "../../mocks/modules/Reservations/data";
import { ref } from "vue";
import { beforeEach } from "vitest";
import type { ReservationParams, ApiReservationsResponse } from "../types";
import { createPinia, setActivePinia } from "pinia";
import { useReservationStore } from "@/stores/reservation";
import { assertFormattedDataIsNotNull } from "@/helpers/testHelpers";

// use vi hoisted? How to import request module correctly?
// https://github.com/vitest-dev/vitest/discussions/3589

describe("mounting reservationsView", async () => {
	beforeEach(() => {
		vi.mock("@vueuse/router", () => ({
			useRouteQuery: vi.fn(() => ref()),
		}));
		vi.mock("vue-router", async () => {
			const actual = (await vi.importActual("vue-router")) as object;
			return {
				...actual,
				useRoute: vi.fn().mockReturnValue({
					params: { brand_id: "1" },
				}),
				useRouter: vi.fn(() => ref()),
			};
		});
		setActivePinia(createPinia());

	});

	it("When the view is mounted, all its components are rendered", async () => {
		vi.mock("../modules/Request/request", async () => ({
			default: {
				get: async () => getReservationsMock(),
			},
		}));
		const wrapper = await mount(reservationsView);
		await flushPromises();

		const uiFilter = wrapper.find('[data-test="ui-filter"]');
		const uiInput = wrapper.find('[data-test="ui-input"]');
		const uiFilterButton = wrapper.find('[data-test="ui-filterButton"]');
		// const uiExportButton = wrapper.find('[data-test="ui-exportButton"]');
		const uiTable = wrapper.find('[data-test="ui-table"]');
		const uiDropdown = wrapper.find('[data-test="ui-dropdown"]');
		const uiPagination = wrapper.find('[data-test="ui-pagination"]');

		expect(wrapper.exists()).toBe(true);
		expect(uiFilter.isVisible()).toBe(true);
		expect(uiInput.isVisible()).toBe(true);
		expect(uiFilterButton.isVisible()).toBe(true);
		// expect(uiExportButton.isVisible()).toBe(true);
		expect(uiTable.isVisible()).toBe(true);
		expect(uiDropdown.isVisible()).toBe(true);
		expect(uiPagination.isVisible()).toBe(true);
	});
});

// TODO: this test isn't finished, while it works, it isn't taking in the mockedData correctly. We still need to transfer the commented tests from reservation store to the view.
describe("formats data correctly", async () => {
	let store;

	beforeEach(() => {
		setActivePinia(createPinia());
		const mockedData = [
			{
				trace_id: "tAowk949I1de0K9f",
				time: "2023/01/26",
				error: "0",
				error_code: null,
				params: {
					reservation_code: "FYTNL",
				},
			},
			{
				trace_id: "roQDE2a0zTtawIZf",
				time: "2013/05/29",
				error: "1",
				error_code: "INT_2_8",
				params: {
					check_in: "2022/10/12",
					first_name: "Lawson",
				},
				response: {
					error: {
						code: "INT_2_8",
						message: "Any message",
					},
				},
			},
			{
				trace_id: "JLZ5mZNmGyOSEMfe",
				time: "2010/01/23",
				error: "1",
				error_code: "INT_1_2",
				params: {
					check_out: "2023/09/08",
					email: "<EMAIL>",
					first_name: "Shyann",
				},
				response: {
					error: {
						code: "INT_1_2",
					},
				},
			},
			{
				trace_id: "7zMjonVVAujHcr4s",
				time: "2009/04/22",
				error: "0",
				error_code: null,
				params: {
					check_in: "2022/12/05",
					check_out: "2024/04/03",
					email: "<EMAIL>",
					first_name: "Stanley",
					last_name: "Kihn",
					reservation_code: "BJPiT",
				},
			},
			{
				trace_id: "COqCo0gIfY7143QK",
				time: "2011/02/27",
				error: "0",
				error_code: null,
				params: {
					last_name: "Cassin",
				},
			},
		];

		store = useReservationStore();
		store.items = mockedData;

	});

	it("returns an array of formatted data", async () => {
		const wrapper = await mount(reservationsView);
		await flushPromises();

		// When it mounts for this test, makes the getReservations call again and thus uses the data from getReservationsMock(), overriding the store.items = mockedData
		const { formattedReservations } = wrapper.vm;
		assertFormattedDataIsNotNull(formattedReservations)
		expectTypeOf(formattedReservations).toBeArray();
		formattedReservations.forEach((reservation) => {
			expectTypeOf(reservation).toBeObject();
			expect(reservation).toHaveProperty("id");
			expect(reservation).toHaveProperty("row");

			expectTypeOf(reservation.id).toBeNumber();
			expectTypeOf(reservation.row).toBeArray();
		});
	})

});


describe("getReservationsMock function", () => {
	let mockResponse: ApiReservationsResponse;
	beforeEach(() => {
		mockResponse = getReservationsMock();
	});

	it("should return an object with a 'data' property that is a DataItem[]", () => {
		expectTypeOf(mockResponse).toBeObject();
		expect(mockResponse).toHaveProperty("data");
		expectTypeOf(mockResponse.data).toBeArray();

		for (const item of mockResponse.data) {
			// Check if each item has the required properties
			expect(item).toHaveProperty("trace_id");
			expect(item).toHaveProperty("time");
			expect(item).toHaveProperty("error");
			expect(item).toHaveProperty("error_code");
			expect(item).toHaveProperty("params");
			//is it necessary to test types?
			expectTypeOf(item.trace_id).toBeString();
			expectTypeOf(item.time).toBeString();
			expectTypeOf(item.error).toBeBoolean();
			expect(
				typeof item.error_code === "string" || item.error_code === null,
			).toBe(true);
			expectTypeOf(item.params).toBeObject();
		}
	});

	it("should return an object with a 'meta' property that is a Meta type", () => {
		expect(mockResponse).toHaveProperty("meta");

		expect(mockResponse.meta).toHaveProperty("current_page");
		expect(mockResponse.meta).toHaveProperty("from");
		expect(mockResponse.meta).toHaveProperty("last_page");
		expect(mockResponse.meta).toHaveProperty("path");
		expect(mockResponse.meta).toHaveProperty("per_page");
		expect(mockResponse.meta).toHaveProperty("to");
		expect(mockResponse.meta).toHaveProperty("total");

		expectTypeOf(mockResponse.meta.current_page).toBeNumber();
		expectTypeOf(mockResponse.meta.from).toBeNumber();
		expectTypeOf(mockResponse.meta.last_page).toBeNumber();
		expectTypeOf(mockResponse.meta.path).toBeString();
		expectTypeOf(mockResponse.meta.per_page).toBeNumber();
		expectTypeOf(mockResponse.meta.to).toBeNumber();
		expectTypeOf(mockResponse.meta.total).toBeNumber();
	});

	it("should return reservations in 'data' with at least one valid parameter", () => {
		for (const item of mockResponse.data) {
			expect(
				item.params.reservation_code ||
				item.params.first_name ||
				item.params.last_name ||
				item.params.check_in ||
				item.params.check_out ||
				item.params.email,
			).toBeTruthy();

			const paramKeys = Object.keys(item.params) as Array<
				keyof ReservationParams
			>;
			for (const key of paramKeys) {
				expect(typeof item.params[key]).toBe("string");
			}
		}
	});

	it("should return reservations with an error message when error is true", () => {
		for (const item of mockResponse.data) {
			if (item.error) {
				expect(item.error_code).toBeTruthy();
				expect(errorCodes).toContain(item.error_code);
			} else {
				expect(item.error_code).toBeNull();
			}
		}
	});

	it("if the reservation has both check_in and check_out params, check_in date should be  before check_out date", () => {
		for (const item of mockResponse.data) {
			if (item.params.check_in && item.params.check_out) {
				const checkInDate = new Date(item.params.check_in);
				const checkOutDate = new Date(item.params.check_out);
				expect(checkInDate.getTime()).toBeLessThan(checkOutDate.getTime());
			}
		}
	});
});
