import { describe, it, expect, expectTypeOf, vi } from "vitest";
import { beforeEach } from "vitest";
import { assertFormattedDataIsNotNull } from "@/helpers/testHelpers";
import { useReservationStore } from "@/stores/reservation";
import { useBrandStore } from "@/stores/brand";
import { createPinia, setActivePinia } from "pinia";
import flushPromises from "flush-promises";
import { mount } from "@vue/test-utils";
import ReservationsViewVue from "@/views/ReservationsView.vue";
import { ref } from "vue";
import messages from "@/locales/en";
import mocks from "@/../mocks/modules/Reservations/reservationMock.json";
import { t } from "@/locales";

describe("formats data correctly", async () => {
	beforeEach(() => {
		vi.mock("@vueuse/router", () => ({
			useRouteQuery: vi.fn(() => ref()),
		}));
		vi.mock("vue-router", async () => {
			const actual = (await vi.importActual("vue-router")) as object;
			return {
				...actual,
				useRoute: vi.fn().mockReturnValue({
					params: { brand_id: "1" },
				}),
				useRouter: vi.fn(() => ref()),
			};
		});
		setActivePinia(createPinia());
		const reservationStore = useReservationStore();
		const brandStore = useBrandStore();

		reservationStore.getReservations = vi.fn();
		brandStore.$state.brands = [
			{
				brandInfo: {
					id: 1,
					time_zone: "Europe/Madrid",
				},
			},
		];
	});

	it("returns an array of formatted data and adapts header", async () => {
		const reservationStore = useReservationStore();
		reservationStore.$state.items = null;

		const wrapper = await mount(ReservationsViewVue);

		reservationStore.$state.items = mocks.mockData1;

		await flushPromises();

		const { formattedReservations, header } = wrapper.vm;

		assertFormattedDataIsNotNull(formattedReservations);
		expect(formattedReservations).toStrictEqual(mocks.expectedResponse1);
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.errorCode"), value: "error_code" },
			{ name: t("filterTable.reservationCode"), value: "" },
			{ name: t("filterTable.source.title"), value: "source" },
			{ name: t("filterTable.firstName"), value: "" },
			{ name: t("filterTable.lastName"), value: "" },
			{ name: t("filterTable.checkIn"), value: "" },
			{ name: t("filterTable.checkOut"), value: "" },
			{ name: t("common.email"), value: "" },
			{ name: t("filterTable.manual"), value: "" },
		]);
		formattedReservations.forEach((reservation) => {
			expectTypeOf(reservation).toBeObject();
			expect(reservation).toHaveProperty("id");
			expect(reservation).toHaveProperty("row");

			expectTypeOf(reservation.id).toBeNumber();
			expectTypeOf(reservation.row).toBeArray();
		});
	});

	it("formats data without errors correctly and adapts header", async () => {
		const mockDataSuccess = [
			{
				trace_id: "success1",
				time: "2023-11-16T15:30:00.000Z",
				error: "0",
				source: "autocheckin",
				params: {
					reservation_code: "SUCCESS",
					first_name: "John",
					last_name: "Doe",
					check_in: "2023/02/01",
					check_out: "2023/02/10",
					email: "<EMAIL>",
				},
			},
		];
		const reservationStore = useReservationStore();
		reservationStore.$state.items = null;

		const wrapper = await mount(ReservationsViewVue);
		reservationStore.$state.items = mockDataSuccess;

		await flushPromises();

		const { formattedReservations, header } = wrapper.vm;
		expect(formattedReservations[0].row[0]).toBe("success1");
		expect(formattedReservations[0].row[1]).toBe("2023-11-16 16:30:00");
		expect(formattedReservations[0].row[2].content).toBe("Success");
		expect(formattedReservations[0].row[3]).toBe("SUCCESS");
		expect(formattedReservations[0].row[4]).toBe("Autocheckin");
		expect(formattedReservations[0].row[5]).toBe("John");
		expect(formattedReservations[0].row[6]).toBe("Doe");
		expect(formattedReservations[0].row[7]).toBe("2023/02/01");
		expect(formattedReservations[0].row[8]).toBe("2023/02/10");
		expect(formattedReservations[0].row[9]).toBe("<EMAIL>");
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.reservationCode"), value: "" },
			{ name: t("filterTable.source.title"), value: "source" },
			{ name: t("filterTable.firstName"), value: "" },
			{ name: t("filterTable.lastName"), value: "" },
			{ name: t("filterTable.checkIn"), value: "" },
			{ name: t("filterTable.checkOut"), value: "" },
			{ name: t("common.email"), value: "" },
		]);
	});

	it("formats data with errors correctly, show correct error Messages and adapts header", async () => {
		const reservationStore = useReservationStore();
		reservationStore.$state.items = null;
		const newMock = [mocks.mockData1[1]];

		const wrapper = await mount(ReservationsViewVue);
		reservationStore.$state.items = newMock;

		await flushPromises();

		const { formattedReservations, header } = wrapper.vm;
		const expectedErrorMessage = "Any message";
		const expectedErrorContent = "Response Not Valid";
		expect(formattedReservations[0].row[3].message).toBe(expectedErrorMessage);
		expect(formattedReservations[0].row[3].content).toBe(expectedErrorContent);
		expect(messages.errorMessages[newMock[0].error_code]).toBe(
			expectedErrorContent,
		);
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.errorCode"), value: "error_code" },
			{ name: t("filterTable.source.title"), value: "source" },
			{ name: t("filterTable.firstName"), value: "" },
			{ name: t("filterTable.checkIn"), value: "" },
			{ name: t("filterTable.manual"), value: "" },
		]);
	});
	it("formats success data with missing optional fields correctly and adapts header", async () => {
		const mockDataSuccessMissingFields = [
			{
				trace_id: "success2",
				time: "2023-11-16T15:30:00.000Z",
				error: "0",
				source: "reception",
				params: {
					// Campos como first_name, last_name, etc., están intencionalmente ausentes
					reservation_code: "SUC2023",
				},
			},
		];
		const reservationStore = useReservationStore();
		reservationStore.$state.items = null;

		const wrapper = await mount(ReservationsViewVue);
		reservationStore.$state.items = mockDataSuccessMissingFields;

		await flushPromises();

		const { formattedReservations, header } = wrapper.vm;
		expect(formattedReservations[0].row).toEqual([
			"success2",
			"2023-11-16 16:30:00",
			{ color: "success", content: "Success", type: "tag" },
			"SUC2023",
			"Reception",
		]);
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.reservationCode"), value: "" },
			{ name: t("filterTable.source.title"), value: "source" },
		]);
	});
	it("formats error data with missing error details correctly and adapts header", async () => {
		const mockDataErrorMissingDetails = [
			{
				trace_id: "error1",
				time: "2023-11-16T15:30:00.000Z",
				error: "1",
				source: "autocheckin",
				params: {},
			},
		];
		const reservationStore = useReservationStore();
		reservationStore.$state.items = null;

		const wrapper = await mount(ReservationsViewVue);
		reservationStore.$state.items = mockDataErrorMissingDetails;

		await flushPromises();

		const { formattedReservations, header } = wrapper.vm;

		expect(formattedReservations[0].row).toEqual([
			"error1",
			"2023-11-16 16:30:00",
			{ color: "danger", content: "Error", type: "tag" },
			"Autocheckin",
		]);
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.source.title"), value: "source" },
		]);
	});
	it("formats data with all optional fields present correctly and adapts header", async () => {
		const mockDataAllFieldsPresent = [
			{
				trace_id: "full1",
				time: "2023-11-16T15:30:00.000Z",
				error: "0",
				source: "reception",
				params: {
					reservation_code: "FULL2023",
					first_name: "Alice",
					last_name: "Wonderland",
					check_in: "2023/05/05",
					check_out: "2023/06/06",
					email: "<EMAIL>",
					manual: false,
					room_number: "0504",
				},
			},
		];

		const reservationStore = useReservationStore();
		reservationStore.$state.items = null;

		const wrapper = await mount(ReservationsViewVue);
		reservationStore.$state.items = mockDataAllFieldsPresent;

		await flushPromises();

		const { formattedReservations, header } = wrapper.vm;
		const expectedRow = [
			"full1",
			"2023-11-16 16:30:00",
			{ color: "success", content: "Success", type: "tag" },
			"FULL2023",
			"Reception",
			"0504",
			"Alice",
			"Wonderland",
			"2023/05/05",
			"2023/06/06",
			"<EMAIL>",
			"No",
		];
		expect(formattedReservations[0].row).toEqual(expectedRow);
		expect(header).toEqual([
			{ name: t("filterTable.sessionId"), value: "trace_id" },
			{ name: t("filterTable.date"), value: "time" },
			{ name: t("filterTable.result"), value: "error" },
			{ name: t("filterTable.reservationCode"), value: "" },
			{ name: t("filterTable.source.title"), value: "source" },
			{ name: t("filterTable.roomNumber"), value: "" },
			{ name: t("filterTable.firstName"), value: "" },
			{ name: t("filterTable.lastName"), value: "" },
			{ name: t("filterTable.checkIn"), value: "" },
			{ name: t("filterTable.checkOut"), value: "" },
			{ name: t("common.email"), value: "" },
			{ name: t("filterTable.manual"), value: "" },
		]);
	});
});
