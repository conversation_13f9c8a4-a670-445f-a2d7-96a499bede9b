import type { FunnelData } from "@/types";
import { orderedPages } from "@/pages";
import { t } from "@/locales";

const getFunnelPages = (data: FunnelData[] | null): Object => {
	if (!data) return [];

	const sortedData = getSortedPages(data);
	const labelArray = sortedData.map((i) => {
		return getFormattedPage(i.page);
	});

	return {
		xaxis: {
			categories: labelArray,
		},
	};
};

const getFunnelSeries = (data: FunnelData[] | null): Object[] => {
	if (!data) return [];

	const sortedData = getSortedPages(data);
	const totalArray = sortedData.map((i) => {
		return i.total;
	});

	return [
		{
			name: "Visits",
			data: totalArray,
		},
	];
};

const getFormattedPage = (page: string): string => {
	const translation = t(`funnelPages.${page}`);
	return translation === `funnelPages.${page}` ? page : translation;
};

const getSortedPages = (data: FunnelData[]): (FunnelData | undefined)[] => {
	return orderedPages
		.map((i) => data.find((j) => j?.page === i))
		.filter((item) => item !== undefined);
};

export { getFunnelPages, getFunnelSeries };
