import { vi, describe, it, expect, beforeEach } from "vitest";
import getOverview from "@/modules/Overview/Overview";
import { getCurrentDateInUTC } from "@/helpers/dates";
import { subDays, format } from "date-fns";
import { API } from "@aws-amplify/api";

vi.mock("@aws-amplify/api", () => ({
	API: {
		get: vi.fn(),
	},
}));

describe("Overview module", async () => {
	let date_from: string;
	let date_to: string;
	let previous_date_from: string;

	beforeEach(() => {
		const currentDateUTC = getCurrentDateInUTC()
		
		date_from = format(
			subDays(currentDateUTC, 1), "yyyy-MM-dd HH:mm:ss"
		);
		previous_date_from = format(
			subDays(currentDateUTC, 2),
			"yyyy-MM-dd HH:mm:ss",
		);
		date_to = format(currentDateUTC, "yyyy-MM-dd HH:mm:ss");
	});

	it("getOverview with no range param should make api call with interval setted in '1h'", () => {
		const brand_id = 1;
		API.get.mockResolvedValue({ data: [] });

		getOverview({ brand_id });
		expect(API.get).toHaveBeenCalledWith(
			"stats",
			`autocheckin/brands/${brand_id}/stats/overview`,
			{
				queryStringParameters: {
					interval: "1h",
					date_from,
					date_to,
					previous_date_from,
					event: ["request", "pageView"],
				},
			},
		);
	});

	it("getOverview with a provided range should make api call with interval setted correctly", () => {
		const name = "reservation";
		const range = "7d";
		const brand_id = 1;
		const currentDateUTC = getCurrentDateInUTC();
		date_from = format(subDays(currentDateUTC, 7), "yyyy-MM-dd");
		previous_date_from = format(
			subDays(currentDateUTC, 14),
			"yyyy-MM-dd",
		);
		date_to = format(currentDateUTC, "yyyy-MM-dd HH:mm:ss");
		API.get.mockResolvedValue({ data: [] });

		getOverview({ brand_id, name, range });
		expect(API.get).toHaveBeenCalledWith(
			"stats",
			`autocheckin/brands/${brand_id}/stats/overview`,
			{
				queryStringParameters: {
					date_from,
					date_to,
					previous_date_from,
					interval: "1d",
					event: ["request"],
					name,
				},
			},
		);
	});

	it("getOverview with no brand_id param should return an empty array", async () => {
		const range = "24h";
		const result = await getOverview({ range });
		await expect(result).toStrictEqual([]);
	});

	it("if API call is rejected, an error should be thrown", async () => {
		const brand_id = 1;
		API.get.mockRejectedValue();
		await expect(getOverview({ brand_id })).rejects.toThrow(
			new Error("Failed to fetch overview"),
		);
	});

	it("getOverview with a provided name, included in requests array, should set queryStringParameters properties correctly", () => {
		const name = "reservation";
		const brand_id = 1;
		API.get.mockResolvedValue({ data: [] });

		getOverview({ brand_id, name });
		expect(API.get).toHaveBeenCalledWith(
			"stats",
			`autocheckin/brands/${brand_id}/stats/overview`,
			{
				queryStringParameters: {
					interval: "1h",
					date_from,
					date_to,
					previous_date_from,
					event: ["request"],
					name,
				},
			},
		);
	});

	it("getOverview with a provided name, included in funnels array, should set queryStringParameters properties correctly", () => {
		const name = "search_funnel";
		const brand_id = 1;
		API.get.mockResolvedValue({ data: [] });

		getOverview({ brand_id, name });
		expect(API.get).toHaveBeenCalledWith(
			"stats",
			`autocheckin/brands/${brand_id}/stats/overview`,
			{
				queryStringParameters: {
					interval: "1h",
					date_from,
					date_to,
					previous_date_from,
					event: ["pageView"],
				},
			},
		);
	});

	it("getOverview with a provided name, not included in requests or funnels array, should set queryStringParameters properties correctly", () => {
		const name = "reservation_errors";
		const brand_id = 1;
		API.get.mockResolvedValue({ data: [] });

		getOverview({ brand_id, name });
		expect(API.get).toHaveBeenCalledWith(
			"stats",
			`autocheckin/brands/${brand_id}/stats/overview`,
			{
				queryStringParameters: {
					interval: "1h",
					date_from,
					date_to,
					previous_date_from,
					event: ["checkinSource"],
				},
			},
		);
	});

	it("getOverview with no provided name and the reception mode enabled, should set queryStringParameters properties correctly", () => {
		const brand_id = 1;
		API.get.mockResolvedValue({ data: [] });

		getOverview({ brand_id, isReceptionModeEnabled: true });
		expect(API.get).toHaveBeenCalledWith(
			"stats",
			`autocheckin/brands/${brand_id}/stats/overview`,
			{
				queryStringParameters: {
					interval: "1h",
					date_from,
					date_to,
					previous_date_from,
					event: ["request", "pageView", "checkinSource"],
				},
			},
		);
	});

	it("getOverview with no provided name and the reception mode disabled, should set queryStringParameters properties correctly", () => {
		const brand_id = 1;
		API.get.mockResolvedValue({ data: [] });

		getOverview({ brand_id, isReceptionModeEnabled: false });
		expect(API.get).toHaveBeenCalledWith(
			"stats",
			`autocheckin/brands/${brand_id}/stats/overview`,
			{
				queryStringParameters: {
					interval: "1h",
					date_from,
					date_to,
					previous_date_from,
					event: ["request", "pageView"],
				},
			},
		);
	});
});
