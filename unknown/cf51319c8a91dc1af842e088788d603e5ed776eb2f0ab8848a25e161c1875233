{"data": [{"active": true, "brandInfo": {"name": "Hotel Grand Luxury", "logo": "https://st2.depositphotos.com/3800167/8112/v/950/depositphotos_81125398-stock-illustration-luxury-hotel-logo.jpg", "id": 76}, "config": {"partial_checkin": false, "room_type_selection": false, "time_limit_checkin": 7, "activate_time_limit": false, "disable_address_autocomplete": false, "reservation_holder_not_modifiable": false, "max_attempts_reservation": 6, "max_attempts_child": 3, "show_holder": true, "show_modal_in_confirmation_page": false, "optional_scan": true, "send_identity_documents_to_PMS": true, "identification": {"child_form": [[{"name": "name", "active": "true", "type": "text", "required": "true", "maxLength": "50", "minLength": "2"}, {"name": "surname", "active": "true", "type": "text", "required": "true", "maxLength": "50", "minLength": "2"}, {"name": "birthday", "active": "true", "type": "date", "required": "true"}, {"name": "nationality", "active": "true", "countryInput": "true", "type": "autocomplete", "required": "false"}, {"name": "kinship", "options": ["son", "nephew", "brother", "cousin", "other"], "active": "true", "type": "select", "required": "true"}]], "reservation_filters": [[{"name": "check_in", "type": "date", "position": 1}], [{"name": "check_out", "type": "date", "position": 2}], [{"name": "reservation_code", "type": "text", "position": 4}], [{"name": "first_name", "type": "text", "position": 3}]], "reservation_inputs": [[{"name": "reservation_code", "active": "true", "position": "4", "type": "text", "maxLength": "20", "minLength": "3"}, {"name": "last_name", "active": "false", "position": "2", "type": "text", "maxLength": "50", "minLength": "2"}, {"name": "check_in", "active": "false", "position": "5", "type": "date"}, {"name": "check_out", "active": "false", "position": "6", "type": "date"}, {"name": "email", "active": "false", "position": "1", "type": "email"}, {"name": "first_name", "active": "false", "position": "6", "type": "text", "maxLength": "50", "minLength": "2"}], [{"name": "reservation_code", "active": "false", "position": "4", "type": "text", "maxLength": "20", "minLength": "3"}, {"name": "last_name", "active": "true", "position": "2", "type": "text", "maxLength": "50", "minLength": "2"}, {"name": "check_in", "active": "true", "position": "5", "type": "date"}, {"name": "check_out", "active": "false", "position": "6", "type": "date"}, {"name": "email", "active": "false", "position": "1", "type": "email"}, {"name": "first_name", "active": "false", "position": "6", "type": "text", "maxLength": "50", "minLength": "2"}]], "validate_data_scan": [[{"fill_from_holder": "false", "minLength": "0", "name": "name", "active": "true", "position": 1, "type": "text", "required": "true", "maxLength": "50"}, {"fill_from_holder": "false", "minLength": "2", "name": "surname", "active": "true", "position": 2, "type": "text", "required": "true", "maxLength": "50"}, {"fill_from_holder": "false", "minLength": "2", "name": "second_surname", "active": "true", "position": 3, "type": "text", "required": "false", "maxLength": "50"}, {"name": "birthday_date", "active": "true", "fill_from_holder": "false", "position": 4, "type": "date", "required": "true"}, {"fill_from_holder": "false", "name": "gender", "options": ["male", "female"], "active": "true", "position": 5, "type": "select", "required": "true"}, {"fill_from_holder": "false", "name": "document_type", "options": ["identity_card", "passport"], "active": "true", "position": 7, "type": "select", "required": "true"}, {"fill_from_holder": "false", "minLength": "4", "name": "document_number", "active": "true", "position": 8, "type": "text", "required": "true", "maxLength": "20"}, {"name": "date_of_issue", "active": "true", "fill_from_holder": "false", "position": 10, "type": "date", "required": "true"}, {"name": "date_of_expiry", "active": "true", "fill_from_holder": "false", "position": 11, "type": "date", "required": "false"}, {"fill_from_holder": "false", "countryInput": "true", "name": "nationality", "active": "true", "position": 6, "type": "autocomplete", "required": "true"}, {"fill_from_holder": "false", "countryInput": "true", "name": "residence_country", "active": "true", "position": 12, "type": "autocomplete", "required": "true"}, {"fill_from_holder": "false", "minLength": "5", "name": "address", "active": "true", "position": 13, "type": "autocomplete", "required": "false", "maxLength": "500"}, {"fill_from_holder": "true", "minLength": "3", "name": "postal_code", "active": "true", "position": 15, "type": "text", "required": "true", "maxLength": "20"}, {"name": "CCAA", "active": "true", "fill_from_holder": "false", "position": 16, "type": "select", "required": "true"}, {"name": "province", "active": "true", "fill_from_holder": "false", "position": 17, "type": "select", "required": "true"}, {"fill_from_holder": "true", "minLength": "2", "name": "telephone", "active": "true", "position": 20, "type": "phone", "required": "false", "maxLength": "50"}, {"name": "email", "active": "true", "fill_from_holder": "true", "position": 21, "type": "email", "required": "false"}, {"fill_from_holder": "false", "minLength": "2", "name": "municipality", "active": "true", "position": 14, "type": "text", "required": "false", "maxLength": "100"}, {"fill_from_holder": "false", "minLength": "2", "name": "region", "active": "true", "position": 18, "type": "text", "required": "false", "maxLength": "100"}, {"fill_from_holder": "false", "minLength": "2", "name": "subregion", "active": "true", "position": 19, "type": "text", "required": "false", "maxLength": "100"}, {"fill_from_holder": "false", "minLength": "2", "name": "document_support_number", "active": "true", "position": 9, "type": "text", "required": "false", "maxLength": "100"}]]}, "advanced_scan": false, "custom_confirmation_text": true, "max_attempts_telephone": 3, "telephone_notifications": false, "show_send_newsletter_checkbox": true, "close_time_limit_checkin": 1, "send_email_checkin_available": false, "max_attempts_document": 10, "redirect_link": false, "scan_children_like_adults": false, "comments": true, "show_save_phone_in_database_checkbox": true, "show_comments_only_on_holder": false, "active": true, "custom_phone_text": false, "telephone": true, "token_key": "cmD3$s2It4dA", "custom_scan_text": false, "signed_documents": true, "child_required_identity_documents_age": 16, "children_sign_documents": false, "send_signed_documents_to_reception": true, "allow_expired_documents": true, "custom_gdpr_text": false, "show_qr_code": true, "reception_signature": true, "paymentsActive": 1, "send_identity_documents_to_reception": true}}, {"active": true, "brandInfo": {"name": "Hotel Perfect Suite", "logo": "https://media-cdn.tripadvisor.com/media/photo-s/14/6e/4e/c5/hotel-logo.jpg", "id": 83}, "config": {"partial_checkin": false, "room_type_selection": false, "time_limit_checkin": 7, "activate_time_limit": false, "disable_address_autocomplete": false, "reservation_holder_not_modifiable": false, "max_attempts_reservation": 6, "max_attempts_child": 3, "show_holder": true, "show_modal_in_confirmation_page": false, "optional_scan": true, "send_identity_documents_to_PMS": true, "identification": {"child_form": [[{"name": "name", "active": "true", "type": "text", "required": "true", "maxLength": "50", "minLength": "2"}, {"name": "surname", "active": "true", "type": "text", "required": "true", "maxLength": "50", "minLength": "2"}, {"name": "birthday", "active": "true", "type": "date", "required": "true"}, {"name": "nationality", "active": "true", "countryInput": "true", "type": "autocomplete", "required": "false"}, {"name": "kinship", "options": ["son", "nephew", "brother", "cousin", "other"], "active": "true", "type": "select", "required": "true"}]], "reservation_filters": [[{"name": "check_in", "type": "date", "position": 1}], [{"name": "check_out", "type": "date", "position": 2}], [{"name": "reservation_code", "type": "text", "position": 4}], [{"name": "first_name", "type": "text", "position": 3}]], "reservation_inputs": [[{"name": "reservation_code", "active": "true", "position": "4", "type": "text", "maxLength": "20", "minLength": "3"}, {"name": "last_name", "active": "false", "position": "2", "type": "text", "maxLength": "50", "minLength": "2"}, {"name": "check_in", "active": "false", "position": "5", "type": "date"}, {"name": "check_out", "active": "false", "position": "6", "type": "date"}, {"name": "email", "active": "false", "position": "1", "type": "email"}, {"name": "first_name", "active": "false", "position": "6", "type": "text", "maxLength": "50", "minLength": "2"}], [{"name": "reservation_code", "active": "false", "position": "4", "type": "text", "maxLength": "20", "minLength": "3"}, {"name": "last_name", "active": "true", "position": "2", "type": "text", "maxLength": "50", "minLength": "2"}, {"name": "check_in", "active": "true", "position": "5", "type": "date"}, {"name": "check_out", "active": "false", "position": "6", "type": "date"}, {"name": "email", "active": "false", "position": "1", "type": "email"}, {"name": "first_name", "active": "false", "position": "6", "type": "text", "maxLength": "50", "minLength": "2"}]], "validate_data_scan": [[{"fill_from_holder": "false", "minLength": "0", "name": "name", "active": "true", "position": 1, "type": "text", "required": "true", "maxLength": "50"}, {"fill_from_holder": "false", "minLength": "2", "name": "surname", "active": "true", "position": 2, "type": "text", "required": "true", "maxLength": "50"}, {"fill_from_holder": "false", "minLength": "2", "name": "second_surname", "active": "true", "position": 3, "type": "text", "required": "false", "maxLength": "50"}, {"name": "birthday_date", "active": "true", "fill_from_holder": "false", "position": 4, "type": "date", "required": "true"}, {"fill_from_holder": "false", "name": "gender", "options": ["male", "female"], "active": "true", "position": 5, "type": "select", "required": "true"}, {"fill_from_holder": "false", "name": "document_type", "options": ["identity_card", "passport"], "active": "true", "position": 7, "type": "select", "required": "true"}, {"fill_from_holder": "false", "minLength": "4", "name": "document_number", "active": "true", "position": 8, "type": "text", "required": "true", "maxLength": "20"}, {"name": "date_of_issue", "active": "true", "fill_from_holder": "false", "position": 10, "type": "date", "required": "true"}, {"name": "date_of_expiry", "active": "true", "fill_from_holder": "false", "position": 11, "type": "date", "required": "false"}, {"fill_from_holder": "false", "countryInput": "true", "name": "nationality", "active": "true", "position": 6, "type": "autocomplete", "required": "true"}, {"fill_from_holder": "false", "countryInput": "true", "name": "residence_country", "active": "true", "position": 12, "type": "autocomplete", "required": "true"}, {"fill_from_holder": "false", "minLength": "5", "name": "address", "active": "true", "position": 13, "type": "autocomplete", "required": "false", "maxLength": "500"}, {"fill_from_holder": "true", "minLength": "3", "name": "postal_code", "active": "true", "position": 15, "type": "text", "required": "true", "maxLength": "20"}, {"name": "CCAA", "active": "true", "fill_from_holder": "false", "position": 16, "type": "select", "required": "true"}, {"name": "province", "active": "true", "fill_from_holder": "false", "position": 17, "type": "select", "required": "true"}, {"fill_from_holder": "true", "minLength": "2", "name": "telephone", "active": "true", "position": 20, "type": "phone", "required": "false", "maxLength": "50"}, {"name": "email", "active": "true", "fill_from_holder": "true", "position": 21, "type": "email", "required": "false"}, {"fill_from_holder": "false", "minLength": "2", "name": "municipality", "active": "true", "position": 14, "type": "text", "required": "false", "maxLength": "100"}, {"fill_from_holder": "false", "minLength": "2", "name": "region", "active": "true", "position": 18, "type": "text", "required": "false", "maxLength": "100"}, {"fill_from_holder": "false", "minLength": "2", "name": "subregion", "active": "true", "position": 19, "type": "text", "required": "false", "maxLength": "100"}, {"fill_from_holder": "false", "minLength": "2", "name": "document_support_number", "active": "true", "position": 9, "type": "text", "required": "false", "maxLength": "100"}]]}, "advanced_scan": false, "custom_confirmation_text": true, "max_attempts_telephone": 3, "telephone_notifications": false, "show_send_newsletter_checkbox": true, "close_time_limit_checkin": 1, "send_email_checkin_available": false, "max_attempts_document": 10, "redirect_link": false, "scan_children_like_adults": false, "comments": true, "show_save_phone_in_database_checkbox": true, "show_comments_only_on_holder": false, "active": true, "custom_phone_text": false, "telephone": true, "token_key": "cmD3$s2It4dA", "custom_scan_text": false, "signed_documents": true, "child_required_identity_documents_age": 16, "children_sign_documents": false, "send_signed_documents_to_reception": true, "allow_expired_documents": true, "custom_gdpr_text": false, "show_qr_code": true, "reception_signature": true, "paymentsActive": 1, "send_identity_documents_to_reception": true}}, {"active": true, "brandInfo": {"name": "Hotel Almost Perfect Suite", "logo": "https://i.pinimg.com/474x/84/60/23/846023f0e8be513be34573f4ff9ac2f4.jpg", "id": 2}, "config": {"partial_checkin": false, "room_type_selection": false, "time_limit_checkin": 7, "activate_time_limit": false, "disable_address_autocomplete": false, "reservation_holder_not_modifiable": false, "max_attempts_reservation": 6, "max_attempts_child": 3, "show_holder": true, "show_modal_in_confirmation_page": false, "optional_scan": true, "send_identity_documents_to_PMS": true, "identification": {"child_form": [[{"name": "name", "active": "true", "type": "text", "required": "true", "maxLength": "50", "minLength": "2"}, {"name": "surname", "active": "true", "type": "text", "required": "true", "maxLength": "50", "minLength": "2"}, {"name": "birthday", "active": "true", "type": "date", "required": "true"}, {"name": "nationality", "active": "true", "countryInput": "true", "type": "autocomplete", "required": "false"}, {"name": "kinship", "options": ["son", "nephew", "brother", "cousin", "other"], "active": "true", "type": "select", "required": "true"}]], "reservation_filters": [[{"name": "check_in", "type": "date", "position": 1}], [{"name": "check_out", "type": "date", "position": 2}], [{"name": "reservation_code", "type": "text", "position": 4}], [{"name": "first_name", "type": "text", "position": 3}]], "reservation_inputs": [[{"name": "reservation_code", "active": "true", "position": "4", "type": "text", "maxLength": "20", "minLength": "3"}, {"name": "last_name", "active": "false", "position": "2", "type": "text", "maxLength": "50", "minLength": "2"}, {"name": "check_in", "active": "false", "position": "5", "type": "date"}, {"name": "check_out", "active": "false", "position": "6", "type": "date"}, {"name": "email", "active": "false", "position": "1", "type": "email"}, {"name": "first_name", "active": "false", "position": "6", "type": "text", "maxLength": "50", "minLength": "2"}], [{"name": "reservation_code", "active": "false", "position": "4", "type": "text", "maxLength": "20", "minLength": "3"}, {"name": "last_name", "active": "true", "position": "2", "type": "text", "maxLength": "50", "minLength": "2"}, {"name": "check_in", "active": "true", "position": "5", "type": "date"}, {"name": "check_out", "active": "false", "position": "6", "type": "date"}, {"name": "email", "active": "false", "position": "1", "type": "email"}, {"name": "first_name", "active": "false", "position": "6", "type": "text", "maxLength": "50", "minLength": "2"}]], "validate_data_scan": [[{"fill_from_holder": "false", "minLength": "0", "name": "name", "active": "true", "position": 1, "type": "text", "required": "true", "maxLength": "50"}, {"fill_from_holder": "false", "minLength": "2", "name": "surname", "active": "true", "position": 2, "type": "text", "required": "true", "maxLength": "50"}, {"fill_from_holder": "false", "minLength": "2", "name": "second_surname", "active": "true", "position": 3, "type": "text", "required": "false", "maxLength": "50"}, {"name": "birthday_date", "active": "true", "fill_from_holder": "false", "position": 4, "type": "date", "required": "true"}, {"fill_from_holder": "false", "name": "gender", "options": ["male", "female"], "active": "true", "position": 5, "type": "select", "required": "true"}, {"fill_from_holder": "false", "name": "document_type", "options": ["identity_card", "passport"], "active": "true", "position": 7, "type": "select", "required": "true"}, {"fill_from_holder": "false", "minLength": "4", "name": "document_number", "active": "true", "position": 8, "type": "text", "required": "true", "maxLength": "20"}, {"name": "date_of_issue", "active": "true", "fill_from_holder": "false", "position": 10, "type": "date", "required": "true"}, {"name": "date_of_expiry", "active": "true", "fill_from_holder": "false", "position": 11, "type": "date", "required": "false"}, {"fill_from_holder": "false", "countryInput": "true", "name": "nationality", "active": "true", "position": 6, "type": "autocomplete", "required": "true"}, {"fill_from_holder": "false", "countryInput": "true", "name": "residence_country", "active": "true", "position": 12, "type": "autocomplete", "required": "true"}, {"fill_from_holder": "false", "minLength": "5", "name": "address", "active": "true", "position": 13, "type": "autocomplete", "required": "false", "maxLength": "500"}, {"fill_from_holder": "true", "minLength": "3", "name": "postal_code", "active": "true", "position": 15, "type": "text", "required": "true", "maxLength": "20"}, {"name": "CCAA", "active": "true", "fill_from_holder": "false", "position": 16, "type": "select", "required": "true"}, {"name": "province", "active": "true", "fill_from_holder": "false", "position": 17, "type": "select", "required": "true"}, {"fill_from_holder": "true", "minLength": "2", "name": "telephone", "active": "true", "position": 20, "type": "phone", "required": "false", "maxLength": "50"}, {"name": "email", "active": "true", "fill_from_holder": "true", "position": 21, "type": "email", "required": "false"}, {"fill_from_holder": "false", "minLength": "2", "name": "municipality", "active": "true", "position": 14, "type": "text", "required": "false", "maxLength": "100"}, {"fill_from_holder": "false", "minLength": "2", "name": "region", "active": "true", "position": 18, "type": "text", "required": "false", "maxLength": "100"}, {"fill_from_holder": "false", "minLength": "2", "name": "subregion", "active": "true", "position": 19, "type": "text", "required": "false", "maxLength": "100"}, {"fill_from_holder": "false", "minLength": "2", "name": "document_support_number", "active": "true", "position": 9, "type": "text", "required": "false", "maxLength": "100"}]]}, "advanced_scan": false, "custom_confirmation_text": true, "max_attempts_telephone": 3, "telephone_notifications": false, "show_send_newsletter_checkbox": true, "close_time_limit_checkin": 1, "send_email_checkin_available": false, "max_attempts_document": 10, "redirect_link": false, "scan_children_like_adults": false, "comments": true, "show_save_phone_in_database_checkbox": true, "show_comments_only_on_holder": false, "active": true, "custom_phone_text": false, "telephone": true, "token_key": "cmD3$s2It4dA", "custom_scan_text": false, "signed_documents": true, "child_required_identity_documents_age": 16, "children_sign_documents": false, "send_signed_documents_to_reception": true, "allow_expired_documents": true, "custom_gdpr_text": false, "show_qr_code": true, "reception_signature": true, "paymentsActive": 1, "send_identity_documents_to_reception": true}}]}