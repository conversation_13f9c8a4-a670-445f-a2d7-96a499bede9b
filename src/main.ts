import "./assets/main.css";
// import "@hotelinking/ui/dist/uiProd.css";
// import "@hotelinking/ui/dist/ui.css";
import "@hotelinking/ui/dist/style.css";
import { createApp } from "vue";

import App from "./App.vue";
import router from "./router";
import store from "./stores/index";

import ui from "@hotelinking/ui";

import { Amplify } from "aws-amplify";
import AuthConfig from "./aws/auth-config";
import ApiConfig from "@/aws/api-config";
import { i18n } from "./locales";

import { worker } from "../mocks/testBrowser";
if (import.meta.env.VITE_APP_MOCK === "true") {
	worker.start();
}

Amplify.configure({
	Auth: AuthConfig,
	API: ApiConfig,
});
const app = createApp(App);

app.use(i18n);
app.use(store);
app.use(router);
app.use(ui);

app.mount("#app");
