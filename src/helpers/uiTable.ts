import { t } from "@/locales";
import getErrorMessage from "@/modules/Error/getErrorMessage";
import type {
	ErrorResponse,
	ReservationSuccessResponse,
	ScanObject,
	ErrorObject,
	ErrorMessageObject,
	FormattedData,
} from "@/types";
import { formatInTimeZone } from "date-fns-tz";
import { parseISO } from "date-fns";

export const formatData = (
	time: string | undefined,
	error: string,
	response: ErrorResponse | ReservationSuccessResponse | ScanObject | string,
	brandTimeZone: string | undefined,
) => {
	let formattedTime = "-";
	if (time && brandTimeZone) {
		const [datePart] = time.split(".");
		const isoString = `${datePart.replace(" ", "T")}Z`;
		const date = parseISO(isoString);

		formattedTime = formatInTimeZone(
			date,
			brandTimeZone,
			"yyyy-MM-dd HH:mm:ss",
		);
	}

	const errorTag =
		error === "1"
			? {
					content: t("common.error"),
					color: "danger",
					type: "tag",
			  }
			: {
					content: t("common.success"),
					color: "success",
					type: "tag",
			  };

	let errorMessage = "-";

	if (response?.error?.code && response?.error?.message) {
		errorMessage = {
			content: getErrorMessage(response.error.code),
			type: "link",
			emits: "showMessageModal",
			message: response.error.message,
		};
	} else if (response?.error?.code) {
		errorMessage = getErrorMessage(response.error.code);
	}

	return { formattedTime, errorTag, errorMessage };
};

export const formatDataExtended = (
	time: string | undefined,
	error: string,
	source: string,
	response: ErrorResponse | ReservationSuccessResponse | ScanObject | string,
	brandTimeZone: string | undefined,
) => {
	const { formattedTime, errorTag, errorMessage } = formatData(
		time,
		error,
		response,
		brandTimeZone,
	);

	const sourceTranslation =
		source === "reception"
			? t("filterTable.source.types.reception")
			: source === "autocheckin"
			? t("filterTable.source.types.autocheckin")
			: "";

	return { formattedTime, errorTag, errorMessage, sourceTranslation };
};

// Remove columns with only "-" values
export function removeEmptyReservationsValues(rows: FormattedData[]): {
	filteredRows: FormattedData[];
	columnIndicesToRemove: number[];
} {
	if (!rows.length) {
		return {
			filteredRows: [],
			columnIndicesToRemove: [],
		};
	}
	const columnCount = rows[0].row.length; //size of newRow

	const columnIndicesToRemove: number[] = [];

	// Finds the indices of columns that have only "-" values
	for (let col = 0; col < columnCount; col++) {
		let allEmpty = true;

		// Checks if all rows have "-" in the current column
		for (let row = 0; row < rows.length; row++) {
			if (rows[row].row[col] !== "-") {
				allEmpty = false;
				break;
			}
		}

		// If all rows in the column are "-", marks this column for removal
		if (allEmpty) {
			columnIndicesToRemove.push(col);
		}
	}

	const filteredRows: FormattedData[] = rows.map((row) => {
		const filteredRow = filterEmptyFields<
			string | ErrorObject | ErrorMessageObject
		>(row.row, columnIndicesToRemove);

		return {
			...row,
			row: filteredRow,
		};
	});

	return { filteredRows, columnIndicesToRemove };
}

export const filterEmptyFields = <T>(
	data: T[],
	indicesToRemove: number[],
): T[] => {
	if (!indicesToRemove.length) {
		return data;
	}

	return data.filter((_, index) => !indicesToRemove.includes(index));
};
